package com.kevinnovation.detoxlauncher.navigation

import android.content.Context
import android.util.Log
import androidx.activity.compose.BackHandler
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.kevinnovation.detoxlauncher.data.AppModel
import com.kevinnovation.detoxlauncher.data.PreferencesManager
import com.kevinnovation.detoxlauncher.ui.AppDrawerScreen
import com.kevinnovation.detoxlauncher.ui.DRAWER_MODE
import com.kevinnovation.detoxlauncher.ui.HomeScreen
import com.kevinnovation.detoxlauncher.ui.SettingsScreen
import com.kevinnovation.detoxlauncher.utils.AppUtils
import com.kevinnovation.detoxlauncher.viewmodel.MainViewModel
import com.kevinnovation.detoxlauncher.viewmodel.NavigationViewModel

@Composable
fun NavigationGraph(
    navController: NavHostController,
    mainViewModel: MainViewModel,
    navigationViewModel: NavigationViewModel
) {
    val context = LocalContext.current
    
    // Observe navigation events
    val navigationEvent by navigationViewModel.navigationEvent.collectAsState()
    
    // Handle navigation events
    LaunchedEffect(navigationEvent) {
        navigationEvent?.let { event ->
            when (event) {
                is com.kevinnovation.detoxlauncher.viewmodel.NavigationEvent.NavigateToHome -> {
                    navController.navigate(NavigationDestination.Home.route) {
                        popUpTo(NavigationDestination.Home.route) { inclusive = true }
                    }
                }
                is com.kevinnovation.detoxlauncher.viewmodel.NavigationEvent.NavigateToDrawer -> {
                    navController.navigate(
                        NavigationDestination.Drawer.createRoute(event.drawerMode.name)
                    )
                }
                is com.kevinnovation.detoxlauncher.viewmodel.NavigationEvent.NavigateToSettings -> {
                    navController.navigate(NavigationDestination.Settings.route)
                }
                is com.kevinnovation.detoxlauncher.viewmodel.NavigationEvent.NavigateBack -> {
                    if (!navController.popBackStack()) {
                        navController.navigate(NavigationDestination.Home.route) {
                            popUpTo(NavigationDestination.Home.route) { inclusive = true }
                        }
                    }
                }
            }
            navigationViewModel.clearNavigationEvent()
        }
    }

    NavHost(
        navController = navController,
        startDestination = NavigationDestination.Home.route,
        enterTransition = { fadeIn(animationSpec = tween(200)) },
        exitTransition = { fadeOut(animationSpec = tween(200)) }
    ) {
        // Home Screen
        composable(
            route = NavigationDestination.Home.route,
            enterTransition = { fadeIn(animationSpec = tween(300)) },
            exitTransition = { fadeOut(animationSpec = tween(200)) }
        ) {
            HomeScreen(
                viewModel = mainViewModel,
                onSwipeLeft = {
                    Log.d("Navigation", "Swipe left detected")
                    AppUtils.startApp(mainViewModel.swipeLeftApp.value, context)
                },
                onSwipeRight = {
                    Log.d("Navigation", "Swipe right detected")
                    AppUtils.startApp(mainViewModel.swipeRightApp.value, context)
                },
                onSwipeUp = { isLeft ->
                    Log.d("Navigation", "Swipe up detected - opening drawer (left side: $isLeft)")
                    navigationViewModel.navigateToDrawer(DrawerMode.OPEN)
                },
                onSwipeDown = {
                    // Expand notifications panel
                    try {
                        val statusBarService = context.getSystemService("statusbar")
                        val statusBarManager = Class.forName("android.app.StatusBarManager")
                        val expandMethod = statusBarManager.getMethod("expandNotificationsPanel")
                        expandMethod.invoke(statusBarService)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                },
                onSettingsRequested = { 
                    navigationViewModel.navigateToSettings()
                },
                onRemoveFromFavorites = { pkgName ->
                    val preferences = PreferencesManager(context)
                    preferences.removeFavoriteApp(pkgName)
                }
            )
        }

        // App Drawer Screen
        composable(
            route = NavigationDestination.Drawer.route,
            arguments = listOf(
                navArgument(NavigationArgs.DRAWER_MODE) {
                    type = NavType.StringType
                    defaultValue = DrawerMode.OPEN.name
                }
            ),
            enterTransition = {
                fadeIn(
                    animationSpec = tween(
                        durationMillis = 500,
                        easing = androidx.compose.animation.core.FastOutSlowInEasing
                    ),
                    initialAlpha = 0.0f
                )
            },
            exitTransition = {
                fadeOut(
                    animationSpec = tween(
                        durationMillis = 500,
                        easing = androidx.compose.animation.core.FastOutSlowInEasing
                    ),
                    targetAlpha = 0.0f
                )
            }
        ) { backStackEntry ->
            val drawerModeString = backStackEntry.arguments?.getString(NavigationArgs.DRAWER_MODE)
            val drawerMode = DrawerMode.fromString(drawerModeString)
            
            // Load ads when drawer opens
            LaunchedEffect(Unit) {
                mainViewModel.loadAdOnDrawerOpen()
            }

            // Handle back button press
            BackHandler {
                navigationViewModel.navigateToHome()
            }

            AppDrawerScreen(
                viewModel = mainViewModel,
                drawerMode = when (drawerMode) {
                    DrawerMode.OPEN -> DRAWER_MODE.OPEN
                    DrawerMode.CHOOSE_SWIPE_LEFT -> DRAWER_MODE.CHOOSE_SWIPE_LEFT
                    DrawerMode.CHOOSE_SWIPE_RIGHT -> DRAWER_MODE.CHOOSE_SWIPE_RIGHT
                },
                onSelectedApp = { app: AppModel ->
                    when (drawerMode) {
                        DrawerMode.CHOOSE_SWIPE_LEFT -> {
                            mainViewModel.setSwipeLeftApp(app)
                            navigationViewModel.navigateToSettings()
                        }
                        DrawerMode.CHOOSE_SWIPE_RIGHT -> {
                            mainViewModel.setSwipeRightApp(app)
                            navigationViewModel.navigateToSettings()
                        }
                        else -> AppUtils.startApp(app, context)
                    }
                },
                onBack = { 
                    navigationViewModel.navigateBack()
                },
                onSwipeLeft = { 
                    navigationViewModel.navigateBack()
                },
                onAddToFavorites = { pkgName ->
                    val preferences = PreferencesManager(context)
                    preferences.addFavoriteApp(pkgName)
                },
                onRemoveFromFavorites = { pkgName ->
                    val preferences = PreferencesManager(context)
                    preferences.removeFavoriteApp(pkgName)
                },
                isFavorite = { pkgName ->
                    mainViewModel.favoriteApps.value.any { it.packageName == pkgName }
                }
            )
        }

        // Settings Screen
        composable(
            route = NavigationDestination.Settings.route,
            enterTransition = { fadeIn(animationSpec = tween(300)) },
            exitTransition = { fadeOut(animationSpec = tween(200)) }
        ) {
            // Handle back button press
            BackHandler {
                navigationViewModel.navigateToHome()
            }

            SettingsScreen(
                viewModel = mainViewModel,
                view = androidx.compose.ui.platform.LocalView.current,
                onBack = {
                    navigationViewModel.navigateToHome()
                },
                onOpenDrawerLeft = {
                    navigationViewModel.navigateToDrawer(DrawerMode.CHOOSE_SWIPE_LEFT)
                },
                onOpenDrawerRight = {
                    navigationViewModel.navigateToDrawer(DrawerMode.CHOOSE_SWIPE_RIGHT)
                }
            )
        }
    }
}
