package com.kevinnovation.detoxlauncher.data

import androidx.compose.ui.graphics.Color
import com.kevinnovation.detoxlauncher.ui.theme.ClockColors

/**
 * Predefined color themes and presets for the clock
 */
object ColorPresets {
    
    // Popular themes
    val popularThemes = listOf(
        ColorTheme(
            id = "classic_blue",
            name = "Classic Blue",
            primaryColor = Color(0xFF2196F3),
            secondaryColor = Color(0xFF03DAC6),
            category = ThemeCategory.POPULAR
        ),
        ColorTheme(
            id = "sunset_glow",
            name = "Sunset Glow",
            primaryColor = ClockColors.SunsetOrange,
            secondaryColor = ClockColors.SunsetPink,
            tertiaryColor = ClockColors.SunsetYellow,
            gradientDirection = GradientDirection.DIAGONAL_TL_BR,
            category = ThemeCategory.POPULAR
        ),
        ColorTheme(
            id = "ocean_breeze",
            name = "Ocean Breeze",
            primaryColor = ClockColors.OceanBlue,
            secondaryColor = ClockColors.OceanTeal,
            gradientDirection = GradientDirection.HORIZONTAL,
            category = ThemeCategory.POPULAR
        ),
        ColorTheme(
            id = "neon_nights",
            name = "Neon Nights",
            primaryColor = ClockColors.NeonGreen,
            secondaryColor = ClockColors.NeonPink,
            category = ThemeCategory.POPULAR
        )
    )
    
    // Nature themes
    val natureThemes = listOf(
        ColorTheme(
            id = "forest_green",
            name = "Forest Green",
            primaryColor = ClockColors.NatureGreen,
            secondaryColor = ClockColors.NatureBrown,
            category = ThemeCategory.NATURE
        ),
        ColorTheme(
            id = "sky_blue",
            name = "Sky Blue",
            primaryColor = ClockColors.NatureBlue,
            secondaryColor = Color(0xFF87CEEB),
            gradientDirection = GradientDirection.VERTICAL,
            category = ThemeCategory.NATURE
        ),
        ColorTheme(
            id = "autumn_leaves",
            name = "Autumn Leaves",
            primaryColor = ClockColors.AutumnOrange,
            secondaryColor = ClockColors.AutumnRed,
            tertiaryColor = ClockColors.AutumnYellow,
            gradientDirection = GradientDirection.DIAGONAL_TL_BR,
            category = ThemeCategory.AUTUMN
        )
    )
    
    // Neon themes
    val neonThemes = listOf(
        ColorTheme(
            id = "electric_blue",
            name = "Electric Blue",
            primaryColor = ClockColors.NeonBlue,
            secondaryColor = ClockColors.NeonPurple,
            category = ThemeCategory.NEON
        ),
        ColorTheme(
            id = "cyber_punk",
            name = "Cyber Punk",
            primaryColor = ClockColors.NeonGreen,
            secondaryColor = ClockColors.NeonPink,
            gradientDirection = GradientDirection.DIAGONAL_TR_BL,
            category = ThemeCategory.NEON
        ),
        ColorTheme(
            id = "laser_show",
            name = "Laser Show",
            primaryColor = ClockColors.NeonPink,
            secondaryColor = ClockColors.NeonBlue,
            tertiaryColor = ClockColors.NeonGreen,
            gradientDirection = GradientDirection.RADIAL,
            category = ThemeCategory.NEON
        )
    )
    
    // Pastel themes
    val pastelThemes = listOf(
        ColorTheme(
            id = "soft_pink",
            name = "Soft Pink",
            primaryColor = ClockColors.PastelPink,
            secondaryColor = ClockColors.PastelBlue,
            category = ThemeCategory.PASTEL
        ),
        ColorTheme(
            id = "mint_dream",
            name = "Mint Dream",
            primaryColor = ClockColors.PastelGreen,
            secondaryColor = ClockColors.PastelYellow,
            gradientDirection = GradientDirection.HORIZONTAL,
            category = ThemeCategory.PASTEL
        ),
        ColorTheme(
            id = "cotton_candy",
            name = "Cotton Candy",
            primaryColor = ClockColors.PastelPink,
            secondaryColor = ClockColors.PastelBlue,
            tertiaryColor = ClockColors.PastelYellow,
            gradientDirection = GradientDirection.RADIAL,
            category = ThemeCategory.PASTEL
        )
    )
    
    // Monochrome themes
    val monochromeThemes = listOf(
        ColorTheme(
            id = "pure_white",
            name = "Pure White",
            primaryColor = ClockColors.MonoWhite,
            secondaryColor = ClockColors.MonoSilver,
            category = ThemeCategory.MONOCHROME
        ),
        ColorTheme(
            id = "elegant_gray",
            name = "Elegant Gray",
            primaryColor = ClockColors.MonoGray,
            secondaryColor = ClockColors.MonoSilver,
            gradientDirection = GradientDirection.VERTICAL,
            category = ThemeCategory.MONOCHROME
        ),
        ColorTheme(
            id = "classic_black",
            name = "Classic Black",
            primaryColor = ClockColors.MonoBlack,
            secondaryColor = ClockColors.MonoGray,
            category = ThemeCategory.MONOCHROME
        )
    )
    
    // Fire themes
    val fireThemes = listOf(
        ColorTheme(
            id = "flame_red",
            name = "Flame Red",
            primaryColor = ClockColors.FireRed,
            secondaryColor = ClockColors.FireOrange,
            tertiaryColor = ClockColors.FireYellow,
            gradientDirection = GradientDirection.VERTICAL,
            category = ThemeCategory.FIRE
        ),
        ColorTheme(
            id = "ember_glow",
            name = "Ember Glow",
            primaryColor = ClockColors.FireDeep,
            secondaryColor = ClockColors.FireRed,
            gradientDirection = GradientDirection.RADIAL,
            category = ThemeCategory.FIRE
        )
    )
    
    // Ice themes
    val iceThemes = listOf(
        ColorTheme(
            id = "frozen_blue",
            name = "Frozen Blue",
            primaryColor = ClockColors.IceBlue,
            secondaryColor = ClockColors.IceCyan,
            category = ThemeCategory.ICE
        ),
        ColorTheme(
            id = "arctic_white",
            name = "Arctic White",
            primaryColor = ClockColors.IceWhite,
            secondaryColor = ClockColors.IceSilver,
            gradientDirection = GradientDirection.DIAGONAL_TL_BR,
            category = ThemeCategory.ICE
        )
    )
    
    // Galaxy themes
    val galaxyThemes = listOf(
        ColorTheme(
            id = "deep_space",
            name = "Deep Space",
            primaryColor = ClockColors.GalaxyDeep,
            secondaryColor = ClockColors.GalaxyPurple,
            tertiaryColor = ClockColors.GalaxyBlue,
            gradientDirection = GradientDirection.RADIAL,
            category = ThemeCategory.GALAXY
        ),
        ColorTheme(
            id = "nebula_pink",
            name = "Nebula Pink",
            primaryColor = ClockColors.GalaxyPink,
            secondaryColor = ClockColors.GalaxyPurple,
            gradientDirection = GradientDirection.DIAGONAL_TR_BL,
            category = ThemeCategory.GALAXY
        )
    )
    
    // Time-based themes
    val morningTheme = ColorTheme(
        id = "morning_sun",
        name = "Morning Sun",
        primaryColor = Color(0xFFFDD835),
        secondaryColor = Color(0xFFFFB74D),
        gradientDirection = GradientDirection.HORIZONTAL,
        category = ThemeCategory.CUSTOM
    )
    
    val afternoonTheme = ColorTheme(
        id = "afternoon_sky",
        name = "Afternoon Sky",
        primaryColor = Color(0xFF42A5F5),
        secondaryColor = Color(0xFF66BB6A),
        gradientDirection = GradientDirection.VERTICAL,
        category = ThemeCategory.CUSTOM
    )
    
    val eveningTheme = ColorTheme(
        id = "evening_sunset",
        name = "Evening Sunset",
        primaryColor = ClockColors.SunsetOrange,
        secondaryColor = ClockColors.SunsetRed,
        gradientDirection = GradientDirection.DIAGONAL_TL_BR,
        category = ThemeCategory.CUSTOM
    )
    
    val nightTheme = ColorTheme(
        id = "night_sky",
        name = "Night Sky",
        primaryColor = Color(0xFF1A237E),
        secondaryColor = Color(0xFF3F51B5),
        gradientDirection = GradientDirection.RADIAL,
        category = ThemeCategory.CUSTOM
    )
    
    // All themes combined
    val allThemes = popularThemes + natureThemes + neonThemes + pastelThemes + 
                   monochromeThemes + fireThemes + iceThemes + galaxyThemes
    
    // Get themes by category
    fun getThemesByCategory(category: ThemeCategory): List<ColorTheme> {
        return allThemes.filter { it.category == category }
    }
    
    // Get theme by ID
    fun getThemeById(id: String): ColorTheme? {
        return allThemes.find { it.id == id }
    }
    
    // Color blind friendly themes
    val colorBlindFriendlyThemes = listOf(
        ColorTheme(
            id = "cb_blue_yellow",
            name = "Blue & Yellow",
            primaryColor = Color(0xFF1976D2),
            secondaryColor = Color(0xFFFDD835),
            category = ThemeCategory.CUSTOM
        ),
        ColorTheme(
            id = "cb_orange_blue",
            name = "Orange & Blue",
            primaryColor = Color(0xFFFF9800),
            secondaryColor = Color(0xFF2196F3),
            category = ThemeCategory.CUSTOM
        )
    )
}
