package com.kevinnovation.detoxlauncher.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.AlarmClock
import android.provider.CalendarContract
import android.util.Log
import android.view.View
import android.view.WindowInsets
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.kevinnovation.detoxlauncher.data.AppModel
import com.kevinnovation.detoxlauncher.data.PreferencesManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

class AppUtils {

    companion object {
        fun startApp(app: AppModel, context: Context): Boolean {
            if (app.packageName == "NULL") {
                Toast.makeText(
                    context,
                    "No App selected for swipe direction. Go to settings and set your desired app there.",
                    Toast.LENGTH_LONG
                ).show()
                  return false
            }
            try {
                Log.d("AppUtils", "Starting app: ${app.name} (${app.packageName})")
                val pm = context.packageManager
                val launchIntent = pm.getLaunchIntentForPackage(app.packageName)
                if (launchIntent != null) {
                    // Add flags to ensure proper app launching behavior
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    context.startActivity(launchIntent)
                    Log.d("AppUtils", "Successfully started app: ${app.name}")
                    return true
                } else {
                    Log.w("AppUtils", "No launch intent found for: ${app.packageName}")
                    Toast.makeText(context, "Unable to start app ${app.name}", Toast.LENGTH_SHORT)
                        .show()
                    return false
                }
            } catch (e: Exception) {
                Log.e("AppUtils", "Error starting app ${app.name}: ${e.message}", e)
                Toast.makeText(context, "Unable to start app ${app.name}", Toast.LENGTH_SHORT)
                    .show()
                return false
            }
        }

        suspend fun removeApp(packageName: String, context: Context) {
            try {

                val intent = Intent(Intent.ACTION_DELETE).apply {
                    data = Uri.parse("package:$packageName")
                    // Wichtig: damit der Dialog korrekt gestartet werden kann
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
                val preferences = PreferencesManager(context)
                preferences.removeFavoriteApp(packageName)
            } catch (e: Exception) {
                Toast.makeText(context, "Unable to remove app $packageName", Toast.LENGTH_SHORT)
                    .show()
            }
        }

        suspend fun openDefaultClockApp(context: Context, onShowAppSelection: ((List<AppModel>) -> Unit)? = null) {
            val preferencesManager = PreferencesManager(context)

            // Check if user has set a preferred clock app
            val preferredClockApp = preferencesManager.preferredClockAppFlow.first()
            if (preferredClockApp != null) {
                if (startApp(preferredClockApp, context)) {
                    return
                } else {
                    // Preferred app failed, clear it and continue with detection
                    preferencesManager.setPreferredClockApp(null)
                }
            }

            val pm = context.packageManager
            Log.d("AppUtils", "Attempting to open clock app")

            // Strategy 1: AlarmClock.ACTION_SHOW_ALARMS
            try {
                val intent = Intent(AlarmClock.ACTION_SHOW_ALARMS).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                if (intent.resolveActivity(pm) != null) {
                    Log.d("AppUtils", "Opening clock via AlarmClock.ACTION_SHOW_ALARMS")
                    context.startActivity(intent)
                    return
                }
            } catch (e: Exception) {
                Log.d("AppUtils", "Failed to open clock via AlarmClock.ACTION_SHOW_ALARMS: ${e.message}")
            }

            // Strategy 2: Intent.CATEGORY_APP_CLOCK
            try {
                val intent = Intent(Intent.ACTION_MAIN).apply {
                    addCategory("android.intent.category.APP_CLOCK")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                if (intent.resolveActivity(pm) != null) {
                    Log.d("AppUtils", "Opening clock via CATEGORY_APP_CLOCK")
                    context.startActivity(intent)
                    return
                }
            } catch (e: Exception) {
                Log.d("AppUtils", "Failed to open clock via CATEGORY_APP_CLOCK: ${e.message}")
            }

            // Strategy 3: Try common clock app package names
            val commonClockPackages = getCommonClockPackages()
            for (packageName in commonClockPackages) {
                try {
                    val launchIntent = pm.getLaunchIntentForPackage(packageName)
                    if (launchIntent != null) {
                        Log.d("AppUtils", "Opening clock app: $packageName")
                        launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        context.startActivity(launchIntent)
                        return
                    }
                } catch (e: Exception) {
                    Log.d("AppUtils", "Failed to open clock app $packageName: ${e.message}")
                }
            }

            // Strategy 4: Find all potential clock apps and show selection dialog
            val clockApps = findClockApps(context)
            if (clockApps.isNotEmpty() && onShowAppSelection != null) {
                Log.d("AppUtils", "Found ${clockApps.size} potential clock apps, showing selection dialog")
                onShowAppSelection(clockApps)
            } else {
                Toast.makeText(context, "Keine Uhr-App gefunden", Toast.LENGTH_SHORT).show()
            }
        }

        suspend fun openDefaultCalendarApp(context: Context, onShowAppSelection: ((List<AppModel>) -> Unit)? = null) {
            val preferencesManager = PreferencesManager(context)

            // Check if user has set a preferred calendar app
            val preferredCalendarApp = preferencesManager.preferredCalendarAppFlow.first()
            if (preferredCalendarApp != null) {
                if (startApp(preferredCalendarApp, context)) {
                    return
                } else {
                    // Preferred app failed, clear it and continue with detection
                    preferencesManager.setPreferredCalendarApp(null)
                }
            }

            val pm = context.packageManager
            Log.d("AppUtils", "Attempting to open calendar app")

            // Strategy 1: Intent.CATEGORY_APP_CALENDAR
            try {
                val intent = Intent(Intent.ACTION_MAIN).apply {
                    addCategory(Intent.CATEGORY_APP_CALENDAR)
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                if (intent.resolveActivity(pm) != null) {
                    Log.d("AppUtils", "Opening calendar via CATEGORY_APP_CALENDAR")
                    context.startActivity(intent)
                    return
                }
            } catch (e: Exception) {
                Log.d("AppUtils", "Failed to open calendar via CATEGORY_APP_CALENDAR: ${e.message}")
            }

            // Strategy 2: CalendarContract.ACTION_VIEW_CALENDAR
            try {
                val intent = Intent(Intent.ACTION_VIEW).apply {
                    data = CalendarContract.CONTENT_URI
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                if (intent.resolveActivity(pm) != null) {
                    Log.d("AppUtils", "Opening calendar via CalendarContract")
                    context.startActivity(intent)
                    return
                }
            } catch (e: Exception) {
                Log.d("AppUtils", "Failed to open calendar via CalendarContract: ${e.message}")
            }

            // Strategy 3: Try common calendar app package names
            val commonCalendarPackages = getCommonCalendarPackages()
            for (packageName in commonCalendarPackages) {
                try {
                    val launchIntent = pm.getLaunchIntentForPackage(packageName)
                    if (launchIntent != null) {
                        Log.d("AppUtils", "Opening calendar app: $packageName")
                        launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        context.startActivity(launchIntent)
                        return
                    }
                } catch (e: Exception) {
                    Log.d("AppUtils", "Failed to open calendar app $packageName: ${e.message}")
                }
            }

            // Strategy 4: Find all potential calendar apps and show selection dialog
            val calendarApps = findCalendarApps(context)
            if (calendarApps.isNotEmpty() && onShowAppSelection != null) {
                Log.d("AppUtils", "Found ${calendarApps.size} potential calendar apps, showing selection dialog")
                onShowAppSelection(calendarApps)
            } else {
                Toast.makeText(context, "Keine Kalender-App gefunden", Toast.LENGTH_SHORT).show()
            }
        }

        /**
         * Get list of common clock app package names across different manufacturers
         */
        private fun getCommonClockPackages(): List<String> {
            return listOf(
                // Google
                "com.google.android.deskclock",
                "com.android.deskclock",
                // Samsung
                "com.sec.android.app.clockpackage",
                // Xiaomi/MIUI
                "com.android.deskclock",
                "com.miui.clock",
                // Huawei/EMUI
                "com.android.deskclock",
                "com.huawei.deskclock",
                // OnePlus/OxygenOS
                "com.oneplus.deskclock",
                "com.android.deskclock",
                // LG
                "com.lge.clock",
                // Sony
                "com.sonyericsson.organizer",
                "com.sonymobile.organizer",
                // HTC
                "com.htc.android.worldclock",
                // Motorola
                "com.motorola.blur.alarmclock",
                // ASUS
                "com.asus.deskclock",
                // Oppo/ColorOS
                "com.coloros.alarmclock",
                "com.oppo.alarmclock",
                // Vivo/FuntouchOS
                "com.vivo.alarmclock",
                // Realme
                "com.coloros.alarmclock",
                // Generic Android
                "com.android.alarmclock"
            )
        }

        /**
         * Get list of common calendar app package names across different manufacturers
         */
        private fun getCommonCalendarPackages(): List<String> {
            return listOf(
                // Google
                "com.google.android.calendar",
                "com.android.calendar",
                // Samsung
                "com.samsung.android.calendar",
                // Xiaomi/MIUI
                "com.miui.calendar",
                "com.android.calendar",
                // Huawei/EMUI
                "com.huawei.calendar",
                "com.android.calendar",
                // OnePlus/OxygenOS
                "com.oneplus.calendar",
                "com.android.calendar",
                // LG
                "com.lge.calendar",
                // Sony
                "com.sonyericsson.calendar",
                "com.sonymobile.calendar",
                // HTC
                "com.htc.calendar",
                // Motorola
                "com.motorola.blur.calendar",
                // ASUS
                "com.asus.calendar",
                // Oppo/ColorOS
                "com.coloros.calendar",
                "com.oppo.calendar",
                // Vivo/FuntouchOS
                "com.vivo.calendar",
                // Realme
                "com.coloros.calendar",
                // Microsoft Outlook
                "com.microsoft.office.outlook",
                // Generic Android
                "com.android.providers.calendar"
            )
        }

        /**
         * Find all potential clock apps on the device
         */
        private fun findClockApps(context: Context): List<AppModel> {
            val pm = context.packageManager
            val clockApps = mutableListOf<AppModel>()

            // Check apps that respond to clock intents
            try {
                val alarmIntent = Intent(AlarmClock.ACTION_SHOW_ALARMS)
                val alarmApps = pm.queryIntentActivities(alarmIntent, 0)
                alarmApps.forEach { resolveInfo ->
                    val appName = resolveInfo.loadLabel(pm).toString()
                    val packageName = resolveInfo.activityInfo.packageName
                    clockApps.add(AppModel(appName, packageName))
                }
            } catch (e: Exception) {
                Log.e("AppUtils", "Error finding alarm apps: ${e.message}")
            }

            try {
                val clockIntent = Intent(Intent.ACTION_MAIN).apply {
                    addCategory("android.intent.category.APP_CLOCK")
                }
                val clockCategoryApps = pm.queryIntentActivities(clockIntent, 0)
                clockCategoryApps.forEach { resolveInfo ->
                    val appName = resolveInfo.loadLabel(pm).toString()
                    val packageName = resolveInfo.activityInfo.packageName
                    val app = AppModel(appName, packageName)
                    if (!clockApps.any { it.packageName == packageName }) {
                        clockApps.add(app)
                    }
                }
            } catch (e: Exception) {
                Log.e("AppUtils", "Error finding clock category apps: ${e.message}")
            }

            // Check common clock packages that are installed
            getCommonClockPackages().forEach { packageName ->
                try {
                    val appInfo = pm.getApplicationInfo(packageName, 0)
                    val appName = pm.getApplicationLabel(appInfo).toString()
                    val app = AppModel(appName, packageName)
                    if (!clockApps.any { it.packageName == packageName }) {
                        clockApps.add(app)
                    }
                } catch (e: Exception) {
                    // App not installed, ignore
                }
            }

            return clockApps.distinctBy { it.packageName }
        }

        /**
         * Find all potential calendar apps on the device
         */
        private fun findCalendarApps(context: Context): List<AppModel> {
            val pm = context.packageManager
            val calendarApps = mutableListOf<AppModel>()

            // Check apps that respond to calendar intents
            try {
                val calendarIntent = Intent(Intent.ACTION_MAIN).apply {
                    addCategory(Intent.CATEGORY_APP_CALENDAR)
                }
                val calendarCategoryApps = pm.queryIntentActivities(calendarIntent, 0)
                calendarCategoryApps.forEach { resolveInfo ->
                    val appName = resolveInfo.loadLabel(pm).toString()
                    val packageName = resolveInfo.activityInfo.packageName
                    calendarApps.add(AppModel(appName, packageName))
                }
            } catch (e: Exception) {
                Log.e("AppUtils", "Error finding calendar category apps: ${e.message}")
            }

            try {
                val calendarViewIntent = Intent(Intent.ACTION_VIEW).apply {
                    data = CalendarContract.CONTENT_URI
                }
                val calendarViewApps = pm.queryIntentActivities(calendarViewIntent, 0)
                calendarViewApps.forEach { resolveInfo ->
                    val appName = resolveInfo.loadLabel(pm).toString()
                    val packageName = resolveInfo.activityInfo.packageName
                    val app = AppModel(appName, packageName)
                    if (!calendarApps.any { it.packageName == packageName }) {
                        calendarApps.add(app)
                    }
                }
            } catch (e: Exception) {
                Log.e("AppUtils", "Error finding calendar view apps: ${e.message}")
            }

            // Check common calendar packages that are installed
            getCommonCalendarPackages().forEach { packageName ->
                try {
                    val appInfo = pm.getApplicationInfo(packageName, 0)
                    val appName = pm.getApplicationLabel(appInfo).toString()
                    val app = AppModel(appName, packageName)
                    if (!calendarApps.any { it.packageName == packageName }) {
                        calendarApps.add(app)
                    }
                } catch (e: Exception) {
                    // App not installed, ignore
                }
            }

            return calendarApps.distinctBy { it.packageName }
        }

        // Backward compatibility wrapper functions
        fun openDefaultClockAppSync(context: Context) {
            CoroutineScope(Dispatchers.Main).launch {
                openDefaultClockApp(context, null)
            }
        }

        fun openDefaultCalendarAppSync(context: Context) {
            CoroutineScope(Dispatchers.Main).launch {
                openDefaultCalendarApp(context, null)
            }
        }
    }
}