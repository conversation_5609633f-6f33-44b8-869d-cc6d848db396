package com.kevinnovation.detoxlauncher.viewmodel

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Bundle
import android.text.format.DateFormat
import android.util.Log
import android.widget.Toast
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.font.FontWeight
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdLoader
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.nativead.NativeAd
import com.google.android.ump.UserMessagingPlatform
import com.kevinnovation.detoxlauncher.data.AppModel
import com.kevinnovation.detoxlauncher.data.AppRepository
import com.kevinnovation.detoxlauncher.data.ColorTheme
import com.kevinnovation.detoxlauncher.data.Constants
import com.kevinnovation.detoxlauncher.data.GradientDirection
import com.kevinnovation.detoxlauncher.data.PreferencesManager
import com.kevinnovation.detoxlauncher.data.TimeBasedColors
import com.kevinnovation.detoxlauncher.utils.AppChangeReceiver
import com.kevinnovation.detoxlauncher.utils.ConsentManager
import com.kevinnovation.detoxlauncher.utils.LauncherStatusManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.Calendar
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale

enum class SCREEN {
    HOME, DRAWER, SETTINGS
}

enum class TEXT_ALIGN {
    LEFT, CENTER, RIGHT
}

@SuppressLint("NewApi")
class MainViewModel(application: Application,
                    private val consentManager: ConsentManager) : AndroidViewModel(application) {

    private val repository = AppRepository(application)
    private val preferences = PreferencesManager(application)
    private val launcherStatusManager = LauncherStatusManager(application)

    private lateinit var adLoader: AdLoader
    var nativeAd by mutableStateOf<NativeAd?>(null)
        private set

    // Caching für bessere Performance
    private var cachedAd: NativeAd? = null
    private var adCacheTime = 0L
    private val AD_CACHE_DURATION = 5 * 60 * 1000L // 5 Minuten Cache

    // Maintain ActiveScreen - centralized state management
    private val _activeScreen = MutableStateFlow(SCREEN.HOME)
    val activeScreen: StateFlow<SCREEN> = _activeScreen.asStateFlow()

    // Loading state to prevent UI interaction during heavy operations
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading

    // Launcher status management
    val isDefaultLauncher: StateFlow<Boolean> = launcherStatusManager.isDefaultLauncher

    private var lastScreenUpdateTime = 0L
    private val SCREEN_UPDATE_DEBOUNCE_MS = 300L // Prevent rapid screen changes

    fun updateActiveScreen(screen: SCREEN) {
        val currentTime = System.currentTimeMillis()
        val currentScreen = _activeScreen.value

        // Prevent rapid screen changes that could cause UI confusion
        if (currentTime - lastScreenUpdateTime < SCREEN_UPDATE_DEBOUNCE_MS && currentScreen != screen) {
            Log.d("MainViewModel", "Screen update debounced: $currentScreen -> $screen")
            return
        }

        Log.d("MainViewModel", "Updating active screen: $currentScreen -> $screen")
        _activeScreen.value = screen
        lastScreenUpdateTime = currentTime
    }

    fun getCurrentScreen(): SCREEN = _activeScreen.value

    fun forceUpdateActiveScreen(screen: SCREEN) {
        Log.d("MainViewModel", "Force updating active screen to: $screen")
        _activeScreen.value = screen
        lastScreenUpdateTime = System.currentTimeMillis()
    }

    // Flow aller installierten Apps
    private val allAppsFlow = repository.getInstalledAppsFlow()

    // Favoriten als StateFlow von AppModels
    private val _favoriteApps = MutableStateFlow<List<AppModel>>(emptyList())
    val favoriteApps: StateFlow<List<AppModel>> = _favoriteApps

    private val _textSize = MutableStateFlow(32)
    val textSize = _textSize.asStateFlow()

    private val _textAlignment = MutableStateFlow(TEXT_ALIGN.RIGHT)
    val textAlignment = _textAlignment.asStateFlow()

    private val _textSpacing = MutableStateFlow(12)
    val textSpacing = _textSpacing.asStateFlow()

    // Font weight state, mapped from the PreferencesManager
    private val _fontWeightClock = mutableStateOf(FontWeight.Thin)
    val fontWeightClock: State<FontWeight> = _fontWeightClock
    private val _fontWeightDate = mutableStateOf(FontWeight.Light)
    val fontWeightDate: State<FontWeight> = _fontWeightDate
    private val _fontWeightTexts = mutableStateOf(FontWeight.Thin)
    val fontWeightTexts: State<FontWeight> = _fontWeightTexts

    // Swipe Left / Swipe Right App
    private val _swipeLeftApp = MutableStateFlow(AppModel("Kalender", "com.samsung.android.calendar"))
    val swipeLeftApp = _swipeLeftApp.asStateFlow()

    private val _swipeRightApp = MutableStateFlow(AppModel("Telefon", "com.samsung.android.dialer"))
    val swipeRightApp = _swipeRightApp.asStateFlow()

    private val _textColor = MutableStateFlow(Color.White)
    val textColor = _textColor.asStateFlow()

    private val _primaryColor = MutableStateFlow(Color.Cyan)
    val primaryColor = _primaryColor.asStateFlow()

    private val _secondaryColor = MutableStateFlow(Color.Magenta)
    val secondaryColor = _secondaryColor.asStateFlow()

    // Enhanced color properties
    private val _tertiaryColor = MutableStateFlow<Color?>(null)
    val tertiaryColor = _tertiaryColor.asStateFlow()

    private val _gradientDirection = MutableStateFlow(GradientDirection.HORIZONTAL)
    val gradientDirection = _gradientDirection.asStateFlow()

    private val _currentColorTheme = MutableStateFlow<String?>(null)
    val currentColorTheme = _currentColorTheme.asStateFlow()

    private val _favoriteColorCombinations = MutableStateFlow<List<ColorTheme>>(emptyList())
    val favoriteColorCombinations = _favoriteColorCombinations.asStateFlow()

    // Accessibility properties
    private val _accessibilityHighContrast = MutableStateFlow(false)
    val accessibilityHighContrast = _accessibilityHighContrast.asStateFlow()

    private val _accessibilityColorBlindFriendly = MutableStateFlow(false)
    val accessibilityColorBlindFriendly = _accessibilityColorBlindFriendly.asStateFlow()

    private val _accessibilityAutoBrightness = MutableStateFlow(false)
    val accessibilityAutoBrightness = _accessibilityAutoBrightness.asStateFlow()

    private val _accessibilityMinContrastRatio = MutableStateFlow(4.5f)
    val accessibilityMinContrastRatio = _accessibilityMinContrastRatio.asStateFlow()

    private val _timeBasedColorsEnabled = MutableStateFlow(false)
    val timeBasedColorsEnabled = _timeBasedColorsEnabled.asStateFlow()

    private val _animatedGradientsEnabled = MutableStateFlow(false)
    val animatedGradientsEnabled = _animatedGradientsEnabled.asStateFlow()

    private val _currentTime = MutableStateFlow("00:00")
    val currentTime = _currentTime.asStateFlow()

    private val _currentDate = MutableStateFlow("1970-01-01")
    val currentDate = _currentDate.asStateFlow()

    private val _batteryLevel = MutableStateFlow("-")
    val batteryLevel = _batteryLevel.asStateFlow()

    private val _isLeft = MutableStateFlow(false)
    val isLeft = _isLeft.asStateFlow()

    private val _showStatusBar = MutableStateFlow(true)
    val showStatusBar = _showStatusBar.asStateFlow()

    private val _showFullWeekday = MutableStateFlow(false)
    val showFullWeekday = _showFullWeekday.asStateFlow()

    private val _lockPortraitOrientation = MutableStateFlow(true)
    val lockPortraitOrientation = _lockPortraitOrientation.asStateFlow()

    private val _personalizedApps = MutableStateFlow(false)
    val personalizedApps = _personalizedApps.asStateFlow()

    // Map für benutzerdefinierte App-Namen
    private val _customAppNames = MutableStateFlow<Map<String, String>>(emptyMap())
    val customAppNames: StateFlow<Map<String, String>> = _customAppNames

    // Set für ausgeblendete Apps
    private val _hiddenApps = MutableStateFlow<Set<String>>(emptySet())
    val hiddenApps: StateFlow<Set<String>> = _hiddenApps

    // Einstellung, ob ausgeblendete Apps angezeigt werden sollen
    private val _showHiddenApps = MutableStateFlow(false)
    val showHiddenApps: StateFlow<Boolean> = _showHiddenApps

    // Einstellung, ob modifizierte Apps hervorgehoben werden sollen
    private val _highlightModifiedApps = MutableStateFlow(false)
    val highlightModifiedApps: StateFlow<Boolean> = _highlightModifiedApps

    // Preferred apps flows
    private val _preferredClockApp = MutableStateFlow<AppModel?>(null)
    val preferredClockApp = _preferredClockApp.asStateFlow()

    private val _preferredCalendarApp = MutableStateFlow<AppModel?>(null)
    val preferredCalendarApp = _preferredCalendarApp.asStateFlow()

    var currentLetter by mutableStateOf('*')
        private set

    // Formatter für Datum/Zeit
    private val timeFormatter = getTimeFormatter(application.applicationContext)
    private val dateFormatter = getDateFormatter()

    // Apps als StateFlow: Gruppiert nach dem ersten Buchstaben
    private val _allApps = MutableStateFlow<Map<Char, List<AppModel>>>(emptyMap())
    val allApps: StateFlow<Map<Char, List<AppModel>>> = _allApps

    // Ausgeblendete Apps als StateFlow
    private val _hiddenAppsList = MutableStateFlow<List<AppModel>>(emptyList())
    val hiddenAppsList: StateFlow<List<AppModel>> = _hiddenAppsList

    private val appChangeReceiver = AppChangeReceiver(application.applicationContext) {
        // Delay app loading to prevent UI freezing during screen transitions
        viewModelScope.launch {
            kotlinx.coroutines.delay(500) // Wait for any ongoing UI transitions
            if (!_isLoading.value) {
                loadApps() // Automatisch Apps aktualisieren
            }
        }
    }

    init {
        // Initialisiere Apps beim Start
        loadApps()
        initAds()

        // Reduziere die automatische Reload-Frequenz, da wir beim AppDrawer-Öffnen laden
        viewModelScope.launch {
            while (isActive) {
                delay(10 * 60 * 1000) // Reload Ads every 10 Minutes (reduziert)
                loadAd()
            }
        }

        // Beobachte die Favoriten aus DataStore und mappe sie auf AppModel-Listen
        viewModelScope.launch {
            preferences.favoriteAppsFlow
                .combine(allAppsFlow) { favoritesSet, allApps ->
                    // Filtere allApps nach denen, die im favoritesSet enthalten sind
                    allApps.filter { favoritesSet.contains(it.packageName) }
                }
                .combine(_customAppNames) { favoriteList, customNames ->
                    // Wende benutzerdefinierte Namen auf die Favoriten an
                    favoriteList.map { app ->
                        val customName = customNames[app.packageName]
                        if (customName != null) {
                            app.copy(customName = customName)
                        } else {
                            app
                        }
                    }
                }
                .collect { favoriteList ->
                    // Sortiere die Favoriten nach ihrem Anzeigenamen (benutzerdefiniert oder Standard)
                    val sortedFavorites = favoriteList.sortedBy { it.getDisplayName().lowercase() }
                    _favoriteApps.value = sortedFavorites
                }
        }
        viewModelScope.launch {
            preferences.fontWeightClockFlow.collect { weight ->
                _fontWeightClock.value = weight
            }
        }

        viewModelScope.launch {
            preferences.fontWeightDateFlow.collect { weight ->
                _fontWeightDate.value = weight
            }
        }

        viewModelScope.launch {
            preferences.fontWeightTextsFlow.collect { weight ->
                _fontWeightTexts.value = weight
            }
        }

        viewModelScope.launch {
            preferences.textAlignment.collect { alignment ->
                _textAlignment.value = alignment
            }
        }

        viewModelScope.launch {
            preferences.textSpacingFlow.collect { weight ->
                _textSpacing.value = weight
            }
        }

        viewModelScope.launch {
            preferences.textSize.collect { weight ->
                _textSize.value = weight
            }
        }

        viewModelScope.launch {
            preferences.swipeLeftApp.collect { app ->
                _swipeLeftApp.value = app
            }
        }

        viewModelScope.launch {
            preferences.swipeRightApp.collect { app ->
                _swipeRightApp.value = app
            }
        }

        viewModelScope.launch {
            preferences.textColorFlow.collect { color ->
                Log.d("kevinx", "Update text color to ${color.toArgb()}....")
                _textColor.value = color
            }
        }

        viewModelScope.launch {
            preferences.primaryColorFlow.collect { color ->
                Log.d("kevinx", "Update primary color to ${color.toArgb()}....")
                _primaryColor.value = color
            }
        }

        viewModelScope.launch {
            preferences.secondaryColorFlow.collect { color ->
                Log.d("kevinx", "Update secondary color to ${color.toArgb()}....")
                _secondaryColor.value = color
            }
        }

        // Enhanced color flows
        viewModelScope.launch {
            preferences.tertiaryColorFlow.collect { color ->
                _tertiaryColor.value = color
            }
        }

        viewModelScope.launch {
            preferences.gradientDirectionFlow.collect { direction ->
                _gradientDirection.value = direction
            }
        }

        viewModelScope.launch {
            preferences.currentColorThemeFlow.collect { themeId ->
                _currentColorTheme.value = themeId
            }
        }

        viewModelScope.launch {
            preferences.favoriteColorCombinationsFlow.collect { combinationsJson ->
                _favoriteColorCombinations.value = parseFavoriteColorCombinations(combinationsJson)
            }
        }

        // Accessibility flows
        viewModelScope.launch {
            preferences.accessibilityHighContrastFlow.collect { enabled ->
                _accessibilityHighContrast.value = enabled
            }
        }

        viewModelScope.launch {
            preferences.accessibilityColorBlindFriendlyFlow.collect { enabled ->
                _accessibilityColorBlindFriendly.value = enabled
            }
        }

        viewModelScope.launch {
            preferences.accessibilityAutoBrightnessFlow.collect { enabled ->
                _accessibilityAutoBrightness.value = enabled
            }
        }

        viewModelScope.launch {
            preferences.accessibilityMinContrastRatioFlow.collect { ratio ->
                _accessibilityMinContrastRatio.value = ratio
            }
        }

        viewModelScope.launch {
            preferences.timeBasedColorsEnabledFlow.collect { enabled ->
                _timeBasedColorsEnabled.value = enabled
                if (enabled) {
                    updateTimeBasedColors()
                }
            }
        }

        viewModelScope.launch {
            preferences.animatedGradientsEnabledFlow.collect { enabled ->
                _animatedGradientsEnabled.value = enabled
            }
        }

        viewModelScope.launch {
            preferences.showFullWeekday.collect { showFullWeekday ->
                Log.d("kevinx", "Update status bar visibility ${showStatusBar}....")
                _showFullWeekday.value = showFullWeekday
            }
        }

        viewModelScope.launch {
            preferences.showStatusBar.collect { showStatusBar ->
                Log.d("kevinx", "Update status bar visibility ${showStatusBar}....")
                _showStatusBar.value = showStatusBar
            }
        }

        viewModelScope.launch {
            preferences.lockPortraitOrientation.collect { lockPortraitOrientation ->
                Log.d("kevinx", "Update portrait orientation lock ${lockPortraitOrientation}....")
                _lockPortraitOrientation.value = lockPortraitOrientation
            }
        }

        // Benutzerdefinierte App-Namen laden
        viewModelScope.launch {
            preferences.customAppNamesFlow.collect { customNames ->
                val previousCustomNames = _customAppNames.value
                _customAppNames.value = customNames
                // Apps nur neu laden, wenn sich die benutzerdefinierten Namen tatsächlich geändert haben
                // und nicht beim ersten initialen Load
                if (previousCustomNames != customNames && previousCustomNames.isNotEmpty()) {
                    loadApps()
                }
            }
        }

        // Ausgeblendete Apps laden
        viewModelScope.launch {
            preferences.hiddenAppsFlow.collect { hiddenApps ->
                _hiddenApps.value = hiddenApps
                // Apps neu laden, um die ausgeblendeten Apps zu filtern
                loadApps()
            }
        }

        // Einstellung laden, ob ausgeblendete Apps angezeigt werden sollen
        viewModelScope.launch {
            preferences.showHiddenAppsFlow.collect { showHiddenApps ->
                _showHiddenApps.value = showHiddenApps
                // Apps neu laden, um die Anzeige anzupassen
                loadApps()
            }
        }

        // Einstellung laden, ob modifizierte Apps hervorgehoben werden sollen
        viewModelScope.launch {
            preferences.highlightModifiedAppsFlow.collect { highlightModifiedApps ->
                _highlightModifiedApps.value = highlightModifiedApps
            }
        }

        // Bevorzugte Uhr-App laden
        viewModelScope.launch {
            preferences.preferredClockAppFlow.collect { preferredClockApp ->
                _preferredClockApp.value = preferredClockApp
            }
        }

        // Bevorzugte Kalender-App laden
        viewModelScope.launch {
            preferences.preferredCalendarAppFlow.collect { preferredCalendarApp ->
                _preferredCalendarApp.value = preferredCalendarApp
            }
        }

        // Uhrzeit und Datum regelmäßig aktualisieren
        viewModelScope.launch {
            var lastHour = -1
            while (true) {
                val now = ZonedDateTime.now(ZoneId.systemDefault())
                _currentTime.value = now.format(getTimeFormatter(application.applicationContext))
                _currentDate.value = now.format(getDateFormatter())

                // Check for hour change and update time-based colors if enabled
                val currentHour = now.hour
                if (currentHour != lastHour && _timeBasedColorsEnabled.value) {
                    updateTimeBasedColors()
                    lastHour = currentHour
                }

                delay(1000) // jede Sekunde aktualisieren
            }
        }

        // Battery updates at a slower rate to reduce overhead
        viewModelScope.launch {
            while (true) {
                try {
                    val bm = getApplication<Application>().getSystemService(Context.BATTERY_SERVICE) as BatteryManager
                    val batteryPct = bm.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
                    _batteryLevel.value = "$batteryPct%"
                } catch (e: Exception) {
                    Log.e("MainViewModel", "Error updating battery: ${e.message}", e)
                }
                delay(30000) // Update every 30 seconds instead of every second
            }
        }

        // Akku-Stand beobachten
        registerBatteryReceiver()
    }

    private fun initAds() {
        val context = getApplication<Application>().applicationContext
        adLoader = AdLoader.Builder(context, Constants.AD_UNIT_ID)
            .forNativeAd { ad ->
                nativeAd = ad
                cachedAd = ad // Cache die Anzeige
                lastAdLoadTime = System.currentTimeMillis() // Zeitstempel setzen
                adCacheTime = System.currentTimeMillis() // Cache-Zeitstempel setzen
                Log.d("kevinxads", "Native ad loaded successfully")
            }
            .withAdListener(object : AdListener() {
                override fun onAdFailedToLoad(error: LoadAdError) {
                    nativeAd = null
                    Log.e("kevinxads", "Failed to load ad: ${error.message}")

                    // Fallback: Versuche nach 30 Sekunden erneut zu laden
                    viewModelScope.launch {
                        delay(30000)
                        if (nativeAd == null && consentManager.canRequestAds()) {
                            Log.d("kevinxads", "Retrying ad load after failure...")
                            loadAd()
                        }
                    }
                }

                override fun onAdLoaded() {
                    Log.d("kevinxads", "Ad loaded callback")
                }
            })
            .build()

        loadAd()
    }

    private fun loadAd() {
        Log.d("kevinxads", "Reload Ads...")
        if (consentManager.canRequestAds()) {
            adLoader.loadAd(createAdRequest())
        }
    }

    // Neue Funktion für das Laden beim AppDrawer-Öffnen
    fun loadAdOnDrawerOpen() {
        // Prüfe zuerst Cache
        if (isCachedAdValid()) {
            Log.d("kevinxads", "Using cached ad")
            nativeAd = cachedAd
            return
        }

        // Lade nur neu, wenn die letzte Anzeige älter als 2 Minuten ist oder null ist
        if (nativeAd == null || shouldReloadAd()) {
            Log.d("kevinxads", "Loading ad on drawer open...")
            loadAd()
        }
    }

    private fun isCachedAdValid(): Boolean {
        val currentTime = System.currentTimeMillis()
        return cachedAd != null && (currentTime - adCacheTime) < AD_CACHE_DURATION
    }

    private var lastAdLoadTime = 0L

    private fun shouldReloadAd(): Boolean {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastLoad = currentTime - lastAdLoadTime
        return timeSinceLastLoad > 2 * 60 * 1000 // 2 Minuten
    }


    private fun createAdRequest(): AdRequest {
        val builder = AdRequest.Builder()
        val consentInformation = UserMessagingPlatform.getConsentInformation(getApplication<Application>().applicationContext)
        Log.d("kevinxads", "Can request ads? => ${consentInformation.canRequestAds()}")
        Log.d("kevinxads", "ConsentStatus => ${consentInformation.consentStatus}")
        Log.d("kevinxads", "canRequestAds (via MyConsentManager) => ${consentManager.canRequestAds()}")
        if (!consentManager.canRequestAds()) {
            Log.d("kevinxads", "No personalized ads...")
            // Falls der Nutzer keine personalisierte Werbung erlaubt, als nicht-personalisiert kennzeichnen
            val extras = Bundle()
            extras.putString("npa", "1") // "npa" = Non-Personalized Ads
            builder.addNetworkExtrasBundle(com.google.ads.mediation.admob.AdMobAdapter::class.java, extras)
        }
        return builder.build()
    }

    fun resetConsentManager(activity: Activity) {
        // Reset DSGVO Consent-Dialog
        consentManager.revokeConsent()
        // Show form again
        consentManager.checkConsentAndShowForm(activity) {
            _personalizedApps.value = consentManager.canRequestAds()
        }
        loadAd()

        // Set for preferences persistent
//        viewModelScope.launch {
////            preferences.setShowStatusBar(showStatusBar)
//        }
    }

    override fun onCleared() {
        super.onCleared()
        val TAG_onCLEARED = "onCleared"
        try {
            getApplication<Application>().unregisterReceiver(batteryReceiver)
        } catch (e: Exception) {
            // Andere Fehler abfangen
            Log.e(TAG_onCLEARED, "Fehler beim Abmelden des batteryReceiver: ${e.message}", e)
        }

        // AppChangeReceiver abmelden
        try {
            appChangeReceiver.unregister(getApplication<Application>().applicationContext)
        } catch (e: Exception) {
            // Fehler beim Abmelden abfangen
            Log.w(TAG_onCLEARED, "appChangeReceiver konnte nicht abgemeldet werden: ${e.message}")
        }

        // LauncherStatusManager abmelden
        try {
            launcherStatusManager.unregisterReceiver()
        } catch (e: Exception) {
            Log.w(TAG_onCLEARED, "launcherStatusManager konnte nicht abgemeldet werden: ${e.message}")
        }
    }

    // Apps laden und sortieren mit Optimierungen
    private fun loadApps() {
        viewModelScope.launch {
            try {
                // Prevent multiple concurrent loading operations
                if (_isLoading.value) {
                    Log.d("MainViewModel", "Apps already loading, skipping duplicate request")
                    return@launch
                }

                _isLoading.value = true
                Log.d("MainViewModel", "Starting to load apps")

                repository.getInstalledAppsFlow().collect { apps ->
                // Benutzerdefinierte Namen anwenden, falls vorhanden
                val appsWithCustomNames = apps.map { app ->
                    val customName = _customAppNames.value[app.packageName]
                    if (customName != null) {
                        app.copy(customName = customName)
                    } else {
                        app
                    }
                }

                // Ausgeblendete Apps filtern oder in separate Liste speichern
                val (visibleApps, hiddenAppsList) = if (_showHiddenApps.value) {
                    // Alle Apps anzeigen, wenn showHiddenApps aktiviert ist
                    Pair(appsWithCustomNames, emptyList())
                } else {
                    // Ausgeblendete Apps filtern
                    appsWithCustomNames.partition { app ->
                        !_hiddenApps.value.contains(app.packageName)
                    }
                }

                // Liste der ausgeblendeten Apps aktualisieren
                _hiddenAppsList.value = hiddenAppsList

                // Nach dem ersten Buchstaben des Anzeigenamens gruppieren (benutzerdefiniert oder Standard)
                // und innerhalb jeder Gruppe nach dem Anzeigenamen sortieren
                _allApps.value = visibleApps
                    .groupBy {
                        it.getDisplayName().firstOrNull()?.uppercaseChar() ?: '#'
                    }
                    .mapValues { (_, apps) ->
                        // Sortiere Apps innerhalb jeder Gruppe nach dem Anzeigenamen
                        apps.sortedWith { app1, app2 ->
                            // Verwende Collator für sprachspezifische Sortierung
                            java.text.Collator.getInstance(Locale.getDefault())
                                .compare(app1.getDisplayName(), app2.getDisplayName())
                        }
                    }
            }
            } catch (e: Exception) {
                Log.e("MainViewModel", "Error loading apps: ${e.message}", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    private fun registerBatteryReceiver() {
        val filter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        getApplication<Application>().registerReceiver(batteryReceiver, filter)
    }

    private val batteryReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent == null) return
            try {
                val level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
                val scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
                if (level >= 0 && scale > 0) {
                    val batteryPct = (level / scale.toFloat()) * 100
                    _batteryLevel.value = "${batteryPct.toInt()}%"
                    // Remove the Toast to avoid UI overhead
                    // Toast.makeText(context, _batteryLevel.value, Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                Log.e("MainViewModel", "Error in battery receiver: ${e.message}", e)
            }
        }
    }



    // Funktion zur dynamischen Formatierung basierend auf den Systemeinstellungen
    private fun getTimeFormatter(context: Context): DateTimeFormatter {
        val pattern = if (DateFormat.is24HourFormat(context)) "HH:mm" else "hh:mm"
        return DateTimeFormatter.ofPattern(pattern, Locale.getDefault()).withZone(ZoneId.systemDefault())
    }

    // Funktion zur dynamischen Formatierung basierend auf den Systemeinstellungen
    private fun getDateFormatter(): DateTimeFormatter {
        return if(showFullWeekday.value) {
            DateTimeFormatter.ofPattern("EEEE, dd.MM.yyyy", Locale.getDefault())
        } else {
            DateTimeFormatter.ofPattern("E, dd.MM.yyyy", Locale.getDefault())
        }
    }

    // Update font weight preference
    fun setFontWeightClock(weight: Int) {
        // Set for yourself
        _fontWeightClock.value = FontWeight(weight)
        // Set for preferences persistent
        viewModelScope.launch {
            preferences.setFontWeightClock(weight)
        }
    }

    // Update font weight preference
    fun setFontWeightDate(weight: Int) {
        // Set for yourself
        _fontWeightDate.value = FontWeight(weight)
        // Set for preferences persistent
        viewModelScope.launch {
            preferences.setFontWeightDate(weight)
        }
    }

    // Update font weight preference
    fun setFontWeightTexts(weight: Int) {
        // Set for yourself
        _fontWeightTexts.value = FontWeight(weight)
        // Set for preferences persistent
        viewModelScope.launch {
            preferences.setFontWeightTexts(weight)
        }
    }

    fun updateCurrentLetter(currentLetter: Char?) {
        if (currentLetter != null) {
            this.currentLetter = currentLetter
        }
    }

    fun setTextSpacing(spacing: Int) {
        // Set for yourself
        _textSpacing.value = spacing
        // Set for preferences persistent
        viewModelScope.launch {
            preferences.setTextSpacing(spacing)
        }
    }

    fun setTextAlignment(align: TEXT_ALIGN) {
        // Set for yourself
        _textAlignment.value = align
        // Set for preferences persistent
        viewModelScope.launch {
            preferences.setTextAlignment(align)
        }
    }

    fun setTextSize(size: Int) {
        // Set for yourself
        _textSize.value = size
        // Set for preferences persistent
        viewModelScope.launch {
            preferences.setTextSize(size)
        }
    }

    fun setTextColor(color: Color) {
        // Set for yourself
        _textColor.value = color
        // Set for preferences persistent
        viewModelScope.launch {
            preferences.setTextColor(color.toArgb())
        }
    }

    fun setPrimaryColor(color: Color) {
        // Set for yourself
        _primaryColor.value = color
        // Set for preferences persistent
        viewModelScope.launch {
            preferences.setPrimaryColor(color.toArgb())
        }
    }

    fun setSecondaryColor(color: Color) {
        // Set for yourself
        _secondaryColor.value = color
        // Set for preferences persistent
        viewModelScope.launch {
            preferences.setSecondaryColor(color.toArgb())
        }
    }

    // Enhanced color setter functions
    fun setTertiaryColor(color: Color?) {
        _tertiaryColor.value = color
        viewModelScope.launch {
            preferences.setTertiaryColor(color?.toArgb())
        }
    }

    fun setGradientDirection(direction: GradientDirection) {
        _gradientDirection.value = direction
        viewModelScope.launch {
            preferences.setGradientDirection(direction)
        }
    }

    fun setCurrentColorTheme(themeId: String?) {
        _currentColorTheme.value = themeId
        viewModelScope.launch {
            preferences.setCurrentColorTheme(themeId)
        }
    }

    fun toggleFavoriteTheme(theme: ColorTheme, isFavorite: Boolean) {
        val currentFavorites = _favoriteColorCombinations.value.toMutableList()
        if (isFavorite) {
            if (!currentFavorites.any { it.id == theme.id }) {
                currentFavorites.add(theme)
            }
        } else {
            currentFavorites.removeAll { it.id == theme.id }
        }
        _favoriteColorCombinations.value = currentFavorites

        viewModelScope.launch {
            preferences.setFavoriteColorCombinations(serializeFavoriteColorCombinations(currentFavorites))
        }
    }

    // Accessibility setter functions
    fun setAccessibilityHighContrast(enabled: Boolean) {
        _accessibilityHighContrast.value = enabled
        viewModelScope.launch {
            preferences.setAccessibilityHighContrast(enabled)
        }
    }

    fun setAccessibilityColorBlindFriendly(enabled: Boolean) {
        _accessibilityColorBlindFriendly.value = enabled
        viewModelScope.launch {
            preferences.setAccessibilityColorBlindFriendly(enabled)
        }
    }

    fun setAccessibilityAutoBrightness(enabled: Boolean) {
        _accessibilityAutoBrightness.value = enabled
        viewModelScope.launch {
            preferences.setAccessibilityAutoBrightness(enabled)
        }
    }

    fun setAccessibilityMinContrastRatio(ratio: Float) {
        _accessibilityMinContrastRatio.value = ratio
        viewModelScope.launch {
            preferences.setAccessibilityMinContrastRatio(ratio)
        }
    }

    fun setTimeBasedColorsEnabled(enabled: Boolean) {
        _timeBasedColorsEnabled.value = enabled
        viewModelScope.launch {
            preferences.setTimeBasedColorsEnabled(enabled)
        }
        if (enabled) {
            updateTimeBasedColors()
        }
    }

    fun setAnimatedGradientsEnabled(enabled: Boolean) {
        _animatedGradientsEnabled.value = enabled
        viewModelScope.launch {
            preferences.setAnimatedGradientsEnabled(enabled)
        }
    }

    // Helper functions
    private fun updateTimeBasedColors() {
        val calendar = Calendar.getInstance()
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val suggestedTheme = TimeBasedColors.getSuggestedThemeForTime(hour)

        setPrimaryColor(suggestedTheme.primaryColor)
        setSecondaryColor(suggestedTheme.secondaryColor)
        suggestedTheme.tertiaryColor?.let { setTertiaryColor(it) }
        setGradientDirection(suggestedTheme.gradientDirection)
    }

    private fun parseFavoriteColorCombinations(json: String): List<ColorTheme> {
        if (json.isEmpty()) return emptyList()

        return try {
            // Simple JSON parsing - in a real app you'd use a proper JSON library
            val themes = mutableListOf<ColorTheme>()
            val parts = json.split("|")
            for (part in parts) {
                if (part.isNotEmpty()) {
                    val components = part.split(";")
                    if (components.size >= 4) {
                        val theme = ColorTheme(
                            id = components[0],
                            name = components[1],
                            primaryColor = Color(components[2].toLong(16)),
                            secondaryColor = Color(components[3].toLong(16)),
                            tertiaryColor = if (components.size > 4 && components[4].isNotEmpty())
                                Color(components[4].toLong(16)) else null,
                            gradientDirection = if (components.size > 5)
                                GradientDirection.valueOf(components[5]) else GradientDirection.HORIZONTAL
                        )
                        themes.add(theme)
                    }
                }
            }
            themes
        } catch (e: Exception) {
            emptyList()
        }
    }

    private fun serializeFavoriteColorCombinations(themes: List<ColorTheme>): String {
        return themes.joinToString("|") { theme ->
            "${theme.id};${theme.name};${theme.primaryColor.toArgb().toString(16)};" +
            "${theme.secondaryColor.toArgb().toString(16)};" +
            "${theme.tertiaryColor?.toArgb()?.toString(16) ?: ""};" +
            "${theme.gradientDirection.name}"
        }
    }

    // Apply color theme
    fun applyColorTheme(theme: ColorTheme) {
        setPrimaryColor(theme.primaryColor)
        setSecondaryColor(theme.secondaryColor)
        setTertiaryColor(theme.tertiaryColor)
        setGradientDirection(theme.gradientDirection)
        setCurrentColorTheme(theme.id)
    }

    // Get current theme as ColorTheme object
    fun getCurrentTheme(): ColorTheme {
        return ColorTheme(
            id = _currentColorTheme.value ?: "custom",
            name = "Current",
            primaryColor = _primaryColor.value,
            secondaryColor = _secondaryColor.value,
            tertiaryColor = _tertiaryColor.value,
            gradientDirection = _gradientDirection.value
        )
    }

    fun setShowFullWeekday(showFullWeekday: Boolean) {
        // Set for yourself
        _showFullWeekday.value = showFullWeekday
        // Set for preferences persistent
        viewModelScope.launch {
            preferences.setShowFullWeekday(showFullWeekday)
        }
    }

    fun setShowStatusBar(showStatusBar: Boolean) {
        // Set for yourself
        _showStatusBar.value = showStatusBar
        // Set for preferences persistent
        viewModelScope.launch {
            preferences.setShowStatusBar(showStatusBar)
        }
    }

    fun setLockPortraitOrientation(lockPortraitOrientation: Boolean) {
        // Set for yourself
        _lockPortraitOrientation.value = lockPortraitOrientation
        // Set for preferences persistent
        viewModelScope.launch {
            preferences.setLockPortraitOrientation(lockPortraitOrientation)
        }
    }

    fun setSwipeRightApp(appPackageName: AppModel) {
        // Set for yourself
        _swipeRightApp.value = appPackageName
        // Set for preferences persistent
        viewModelScope.launch {
            preferences.setSwipeRightApp(appPackageName)
        }
    }

    fun setSwipeLeftApp(appPackageName: AppModel) {
        // Set for yourself
        _swipeLeftApp.value = appPackageName
        // Set for preferences persistent
        viewModelScope.launch {
            preferences.setSwipeLeftApp(appPackageName)
        }
    }

    // Funktion zum Setzen eines benutzerdefinierten App-Namens
    fun setCustomAppName(packageName: String, customName: String) {
        viewModelScope.launch {
            preferences.setCustomAppName(packageName, customName)
            // Die Aktualisierung erfolgt automatisch über den Flow
        }
    }

    // Funktion zum Entfernen eines benutzerdefinierten App-Namens
    fun removeCustomAppName(packageName: String) {
        viewModelScope.launch {
            preferences.removeCustomAppName(packageName)
            // Die Aktualisierung erfolgt automatisch über den Flow
        }
    }

    // Funktion zum Ausblenden einer App
    fun hideApp(packageName: String) {
        viewModelScope.launch {
            preferences.hideApp(packageName)
            // Die Aktualisierung erfolgt automatisch über den Flow
        }
    }

    // Funktion zum Einblenden einer ausgeblendeten App
    fun unhideApp(packageName: String) {
        viewModelScope.launch {
            preferences.unhideApp(packageName)
            // Die Aktualisierung erfolgt automatisch über den Flow
        }
    }

    // Funktion zum Setzen der Einstellung, ob ausgeblendete Apps angezeigt werden sollen
    fun setShowHiddenApps(value: Boolean) {
        _showHiddenApps.value = value
        viewModelScope.launch {
            preferences.setShowHiddenApps(value)
        }
        loadApps() // Apps neu laden, um die Änderung anzuwenden
    }

    // Funktion zum Setzen der Einstellung, ob modifizierte Apps hervorgehoben werden sollen
    fun setHighlightModifiedApps(value: Boolean) {
        _highlightModifiedApps.value = value
        viewModelScope.launch {
            preferences.setHighlightModifiedApps(value)
        }
    }

    // Funktion zum Prüfen, ob eine App ausgeblendet ist
    fun isAppHidden(packageName: String): Boolean {
        return _hiddenApps.value.contains(packageName)
    }

    // Funktion zum Setzen der bevorzugten Uhr-App
    fun setPreferredClockApp(app: AppModel?) {
        _preferredClockApp.value = app
        viewModelScope.launch {
            preferences.setPreferredClockApp(app)
        }
    }

    // Funktion zum Setzen der bevorzugten Kalender-App
    fun setPreferredCalendarApp(app: AppModel?) {
        _preferredCalendarApp.value = app
        viewModelScope.launch {
            preferences.setPreferredCalendarApp(app)
        }
    }

    // Funktion zum Prüfen, ob eine App umbenannt wurde
    fun isAppRenamed(packageName: String): Boolean {
        return _customAppNames.value.containsKey(packageName)
    }

    // Launcher Status Management
    fun checkLauncherStatus() {
        launcherStatusManager.checkLauncherStatus()
    }

    fun refreshLauncherStatus() {
        launcherStatusManager.refresh()
    }

    fun enableLauncherDebugLogging() {
        launcherStatusManager.enableDebugLogging()
    }
}
