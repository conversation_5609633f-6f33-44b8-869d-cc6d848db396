package com.kevinnovation.detoxlauncher.ui

import android.annotation.SuppressLint
import android.app.Activity
import android.view.View
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsIgnoringVisibility
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Slider
import androidx.compose.material3.SliderDefaults
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.github.skydoves.colorpicker.compose.BrightnessSlider
import com.github.skydoves.colorpicker.compose.ColorEnvelope
import com.github.skydoves.colorpicker.compose.HsvColorPicker
import com.github.skydoves.colorpicker.compose.rememberColorPickerController
import com.kevinnovation.detoxlauncher.R
import com.kevinnovation.detoxlauncher.data.AppModel
import com.kevinnovation.detoxlauncher.ui.components.FullSizeClockPreview
import com.kevinnovation.detoxlauncher.ui.components.SimpleColorPreview
import com.kevinnovation.detoxlauncher.ui.components.SimpleThemeSelector
import com.kevinnovation.detoxlauncher.ui.components.TimeBasedColorSuggestion
import com.kevinnovation.detoxlauncher.utils.darken
import com.kevinnovation.detoxlauncher.viewmodel.MainViewModel
import com.kevinnovation.detoxlauncher.viewmodel.TEXT_ALIGN
import kotlin.math.roundToInt

const val settingsTextSize = 22

@OptIn(ExperimentalLayoutApi::class)
@SuppressLint("DefaultLocale")
@Composable
fun SettingsScreen(
    viewModel: MainViewModel,
    view: View,
    onBack: () -> Unit,
    onOpenDrawerLeft: () -> Unit,
    onOpenDrawerRight: () -> Unit,
) {

    val textSize by viewModel.textSize.collectAsState()
    val textColor by viewModel.textColor.collectAsState()
    val primaryColor by viewModel.primaryColor.collectAsState()
    val secondaryColor by viewModel.secondaryColor.collectAsState()
    val fontWeightClock by viewModel.fontWeightClock
    val fontWeightDate by viewModel.fontWeightDate
    val fontWeightTexts by viewModel.fontWeightTexts
    val textSpaceing by viewModel.textSpacing.collectAsState()
    val textAlign by viewModel.textAlignment.collectAsState()
    val showStatusBar by viewModel.showStatusBar.collectAsState()
    val showFullWeekday by viewModel.showFullWeekday.collectAsState()
    val lockPortraitOrientation by viewModel.lockPortraitOrientation.collectAsState()

    val swipeLeftApp by viewModel.swipeLeftApp.collectAsState()
    val swipeRightApp by viewModel.swipeRightApp.collectAsState()
    val preferredClockApp by viewModel.preferredClockApp.collectAsState()
    val preferredCalendarApp by viewModel.preferredCalendarApp.collectAsState()

    var selectedPackageForSettings: String
    val alignment = when(textAlign) {
        TEXT_ALIGN.LEFT -> { Alignment.CenterStart }
        TEXT_ALIGN.CENTER -> { Alignment.Center }
        TEXT_ALIGN.RIGHT -> { Alignment.CenterEnd }
    }


    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color.Black.copy(0.4f))
            .verticalScroll(rememberScrollState())
            .padding(WindowInsets.systemBarsIgnoringVisibility.asPaddingValues())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // General
        SettingCard(
            viewModel = viewModel,
            title = stringResource(id = R.string.settings_heading_general),
            style = TextStyle(
                color = textColor,
                fontSize = (textSize + 5).sp,
                fontWeight = fontWeightTexts
            )
        ) {
            SettingSwitch(
                viewModel = viewModel,
                settingName = stringResource(id = R.string.settings_show_statusbar),
                settingValue = showStatusBar,
                onCheckedChange = { newValue ->
                    viewModel.setShowStatusBar(newValue)
                },
            )
            SettingSwitch(
                viewModel = viewModel,
                settingName = stringResource(id = R.string.settings_lock_portrait_orientation),
                settingValue = lockPortraitOrientation,
                onCheckedChange = { newValue ->
                    viewModel.setLockPortraitOrientation(newValue)
                },
            )
        }

        SettingCard(
            viewModel = viewModel,
            title = stringResource(id = R.string.settings_heading_clock_style),
            style = TextStyle(
                color = textColor,
                fontSize = 36.sp,
                fontWeight = fontWeightTexts
            )
        ) {
            DetoxClock(viewModel = viewModel)
            HorizontalDivider(modifier = Modifier.padding(vertical = 16.dp), color = textColor)
            SettingSlider(
                viewModel = viewModel,
                settingName = stringResource(id = R.string.settings_clock_weight),
                sliderValue = fontWeightClock.weight.toFloat(),
                valueTransform = { value -> (value.roundToInt()/100).toString() },
                onValueChange = {
                    viewModel.setFontWeightClock(it.roundToInt())
                },
                steps = 7,
                valueRange = 100f..900f
            )
            SettingSlider(
                viewModel = viewModel,
                settingName = stringResource(id = R.string.settings_date_weight),
                sliderValue = fontWeightDate.weight.toFloat(),
                valueTransform = { value -> (value.roundToInt()/100).toString() },
                onValueChange = {
                    viewModel.setFontWeightDate(it.roundToInt())
                },
                steps = 7,
                valueRange = 100f..900f
            )
            SettingSwitch(
                viewModel = viewModel,
                settingName = stringResource(id = R.string.settings_show_full_weekday),
                settingValue = showFullWeekday,
                onCheckedChange = { newValue ->
                    viewModel.setShowFullWeekday(newValue)
                },
            )
        }

        SettingCard(
            viewModel = viewModel,
            title = stringResource(id = R.string.settings_heading_colors),
            style = TextStyle(
                color = textColor,
                fontSize = (textSize + 5).sp,
                fontWeight = fontWeightTexts
            )
        ) {
            SettingColor(viewModel = viewModel,stringResource(id = R.string.settings_text_color), textColor, onColorChanged = { colorEnvelope: ColorEnvelope ->
                // Aktuelle Farbe aus der Envelope holen
                viewModel.setTextColor(colorEnvelope.color)
            })

            SettingColor(viewModel = viewModel,stringResource(id = R.string.settings_primary_color), primaryColor, onColorChanged = { colorEnvelope: ColorEnvelope ->
                // Aktuelle Farbe aus der Envelope holen
                viewModel.setPrimaryColor(colorEnvelope.color)
            })
            SettingColor(viewModel = viewModel,stringResource(id = R.string.settings_secondary_color), secondaryColor, onColorChanged = { colorEnvelope: ColorEnvelope ->
                // Aktuelle Farbe aus der Envelope holen
                viewModel.setSecondaryColor(colorEnvelope.color)
            })

            Spacer(modifier = Modifier.height(16.dp))

            // Full-Size Live Preview (same size as original clock)
            FullSizeClockPreview(
                primaryColor = primaryColor,
                secondaryColor = secondaryColor,
                modifier = Modifier.fillMaxWidth()
            )


        }

        SettingCard(
            viewModel = viewModel,
            title = "Schnelle Farbthemen",
            style = TextStyle(
                color = textColor,
                fontSize = (textSize + 5).sp,
                fontWeight = fontWeightTexts
            )
        ) {
            SimpleThemeSelector(
                viewModel = viewModel,
                modifier = Modifier.fillMaxWidth()
            )
        }

        SettingCard(
            viewModel = viewModel,
            title = "Zeitbasierte Farbvorschläge",
            style = TextStyle(
                color = textColor,
                fontSize = (textSize + 5).sp,
                fontWeight = fontWeightTexts
            )
        ) {
            TimeBasedColorSuggestion(
                viewModel = viewModel,
                modifier = Modifier.fillMaxWidth()
            )
        }

        SettingCard(
            viewModel = viewModel,
            title = stringResource(id = R.string.settings_heading_text_style),
            style = TextStyle(
                color = textColor,
                fontSize = 36.sp,
                fontWeight = fontWeightTexts
            )
        ) {
            BigLetter(
                viewModel = viewModel,
                modifier = Modifier.fillMaxWidth(),
                fontWeight = fontWeightTexts,
                letter = "K"
            )
            Row(
                modifier = Modifier.padding(
                    8.dp,
                    bottom = textSpaceing.dp
                )
            ) {

                Box(modifier = Modifier.fillMaxWidth(), contentAlignment = alignment) {
                    Text(
                        modifier = Modifier.wrapContentWidth(),
                        text = "App Name 1",
                        style = TextStyle(
                            color = textColor,
                            fontSize = textSize.sp,
                            fontWeight = fontWeightTexts
                        )
                    )
                }
            }
            Row(
                modifier = Modifier.padding(
                    8.dp,
                    top = textSpaceing.dp
                )
            ) {
                Box(modifier = Modifier.fillMaxWidth(), contentAlignment = alignment) {
                    Text(
                        modifier = Modifier.wrapContentWidth(),
                        text = "Settings",
                        style = TextStyle(
                            color = textColor,
                            fontSize = textSize.sp,
                            fontWeight = fontWeightTexts
                        )
                    )
                }
            }
            HorizontalDivider(modifier = Modifier.padding(vertical = 16.dp), color = textColor)
            SettingSlider(
                viewModel = viewModel,
                settingName = stringResource(id = R.string.settings_text_alignment),
                sliderValue = textAlign.ordinal.toFloat(),
                valueTransform = { value ->
                    val id = when (TEXT_ALIGN.entries[value.toInt()]) {
                        TEXT_ALIGN.LEFT -> {R.string.settings_text_alignment_left}
                        TEXT_ALIGN.CENTER -> {R.string.settings_text_alignment_center}
                        TEXT_ALIGN.RIGHT -> {R.string.settings_text_alignment_right}
                    }
                    stringResource(id = id)
                },
                onValueChange = { viewModel.setTextAlignment(TEXT_ALIGN.entries[it.toInt()]) },
                steps = 1,
                valueRange = 0f..2f
            )
            SettingSlider(
                viewModel = viewModel,
                settingName = stringResource(id = R.string.settings_text_weight),
                sliderValue = fontWeightTexts.weight.toFloat(),
                valueTransform = { value -> (value.roundToInt()/100).toString() },
                onValueChange = { viewModel.setFontWeightTexts(it.roundToInt()) },
                steps = 7,
                valueRange = 100f..900f
            )
            SettingSlider(
                viewModel = viewModel,
                settingName = stringResource(id = R.string.settings_text_size),
                sliderValue = textSize.toFloat(),
                onValueChange = { viewModel.setTextSize(it.roundToInt()) },
                steps = 18,
                valueRange = 20f..40f
            )
            SettingSlider(
                viewModel = viewModel,
                settingName = stringResource(id = R.string.settings_text_spacing),
                sliderValue = textSpaceing.toFloat(),
                onValueChange = { viewModel.setTextSpacing(it.roundToInt()) },
                steps = 18,
                valueRange = 0f..20f
            )
        }
        SettingCard(
            viewModel = viewModel,
            title = stringResource(id = R.string.settings_heading_app_preferences),
            style = TextStyle(
                color = textColor,
                fontSize = (textSize + 5).sp,
                fontWeight = fontWeightTexts
            )
        ) {
            AppPreferenceSetting(
                viewModel = viewModel,
                settingName = stringResource(id = R.string.settings_preferred_clock_app),
                currentApp = preferredClockApp,
                onClearApp = { viewModel.setPreferredClockApp(null) }
            )

            AppPreferenceSetting(
                viewModel = viewModel,
                settingName = stringResource(id = R.string.settings_preferred_calendar_app),
                currentApp = preferredCalendarApp,
                onClearApp = { viewModel.setPreferredCalendarApp(null) }
            )
        }

        SettingCard(
            viewModel = viewModel,
            title = stringResource(id = R.string.settings_heading_swipe_gestures),
            style = TextStyle(
                color = textColor,
                fontSize = (textSize + 5).sp,
                fontWeight = fontWeightTexts
            )
        ) {

            SettingOption(viewModel = viewModel, stringResource(id = R.string.settings_swipe_left), swipeLeftApp.name, onOpenDrawerLeft)
            SettingOption(viewModel = viewModel, stringResource(id = R.string.settings_swipe_right), swipeRightApp.name, onOpenDrawerRight)
        }

        SettingCard(
            viewModel = viewModel,
            title = stringResource(id = R.string.settings_heading_dsgvo_consent),
            style = TextStyle(
                color = textColor,
                fontSize = (textSize + 5).sp,
                fontWeight = fontWeightTexts
            )
        ) {

            val context = LocalContext.current
            val activity = context as? Activity
            SettingOption(
                viewModel = viewModel,
                settingName = stringResource(id = R.string.settings_dsgvo_reset_willingness),
                settingValue = stringResource(id = R.string.settings_dsgvo_reset),
                onClick = {
                activity?.let { act ->
                    viewModel.resetConsentManager(act)
                }
            })
        }
        Spacer(modifier = Modifier.padding(20.dp))
    }
}

@Composable
fun SettingCard(
    viewModel: MainViewModel,
    title: String,
    style: TextStyle,
    content: @Composable () -> Unit
) {
    val textColor by viewModel.textColor.collectAsState()
    val primaryColor by viewModel.primaryColor.collectAsState()
    val secondaryColor by viewModel.secondaryColor.collectAsState()
    OutlinedCard(
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent,
        ),
        border = BorderStroke(1.dp, textColor),
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Text(
            text = title,
            style = style,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            textAlign = TextAlign.Left,
        )

        Column(modifier = Modifier.padding(16.dp)) {
            // Hier kannst du eigenen Code einbauen
            // Und schließlich rufst du dein "content" auf
            content()
        }
    }
}

@Composable
fun SettingSwitch(
    viewModel: MainViewModel,
    settingName: String,
    settingValue: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    val textColor by viewModel.textColor.collectAsState()
    val primaryColor by viewModel.primaryColor.collectAsState()
    val secondaryColor by viewModel.secondaryColor.collectAsState()
    val showStatusBar by viewModel.showStatusBar.collectAsState()

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(5.dp),
    ) {
        Row(modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = settingName,
                style = TextStyle(
                    color = textColor,
                    fontSize = settingsTextSize.sp,
                    fontWeight = FontWeight.Normal
                ),
                modifier = Modifier.padding(end = 5.dp)
            )

            Switch(
                colors = SwitchDefaults.colors(
                    checkedTrackColor = primaryColor,
                    checkedThumbColor = secondaryColor.darken(0.3f)),
                checked = settingValue,
                onCheckedChange = onCheckedChange
            )
        }
    }
}

@Composable
fun SettingOption(
    viewModel: MainViewModel,
    settingName: String,
    settingValue: String,
    onClick: () -> Unit
) {
    val textColor by viewModel.textColor.collectAsState()
    val primaryColor by viewModel.primaryColor.collectAsState()
    val secondaryColor by viewModel.secondaryColor.collectAsState()

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(5.dp),
    ) {
        Row(modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = settingName,
                style = TextStyle(
                    color = textColor,
                    fontSize = settingsTextSize.sp,
                    fontWeight = FontWeight.Normal
                ),
                modifier = Modifier.padding(end = 5.dp)
            )

            OutlinedButton(onClick = onClick,
                colors = ButtonDefaults.outlinedButtonColors(
                containerColor = secondaryColor.copy(alpha = 0.5f),   // Hintergrundfarbe
//                contentColor = Color.Black       // Text/Icon-Farbe
            ), border = BorderStroke(1.dp, primaryColor)) {
                Text(
                    text = settingValue,
                    textAlign = TextAlign.End,
                    style = TextStyle(
                        color = textColor,
                        fontSize = settingsTextSize.sp,
                        fontWeight = FontWeight.Light
                    )
                )
            }
        }
    }
}


@Composable
fun SettingSlider(
    viewModel: MainViewModel,
    settingName: String,
    sliderValue: Float,
    valueTransform: @Composable (Float) -> String = { value -> value.toInt().toString() },
    steps: Int,
    onValueChange: (Float) -> Unit,
    valueRange: ClosedFloatingPointRange<Float> = 0f..100f
) {
    val textColor by viewModel.textColor.collectAsState()
    val primaryColor by viewModel.primaryColor.collectAsState()
    val secondaryColor by viewModel.secondaryColor.collectAsState()

    Column(
        modifier = Modifier
            .fillMaxWidth(),
//        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = settingName,
                style = TextStyle(
                    color = textColor,
                    fontSize = settingsTextSize.sp,
                    fontWeight = FontWeight.Normal
                ),
                modifier = Modifier.padding(end = 5.dp)
            )
            Text(
                text = valueTransform(sliderValue),
                style = TextStyle(
                    color = textColor,
                    fontSize = settingsTextSize.sp,
                    fontWeight = FontWeight.Bold
                ),
                modifier = Modifier.padding(end = 5.dp)
            )
        }
        Slider(
            value = sliderValue,
            onValueChange = onValueChange,
            steps = steps,
            valueRange = valueRange,
            colors = SliderDefaults.colors(
                activeTrackColor = primaryColor,
                inactiveTrackColor = secondaryColor,
                thumbColor = textColor
            )
        )
    }
}

@Composable
fun SettingColor(
    viewModel: MainViewModel,
    settingName: String,
    settingValue: Color, // kannst du anpassen, wenn du nur die Farbe brauchst
    modifier: Modifier = Modifier,
    onColorChanged: (ColorEnvelope) -> Unit
) {

    val textColor by viewModel.textColor.collectAsState()
    val primaryColor by viewModel.primaryColor.collectAsState()
    val secondaryColor by viewModel.secondaryColor.collectAsState()

    val controller = rememberColorPickerController()


    // Steuert, ob der ColorPicker angezeigt wird
    var showColorPicker by remember { mutableStateOf(false) }

    // Beispiel: In deinem settingValue könntest du eine Farbe speichern,
    // hier vereinfachend als "selectedColor". Wenn du es dynamisch brauchst,
    // leg dir einen mutableStateOf(Color.Red) o. Ä. an.

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(8.dp)
    ) {
        // Titel + Button in einer Zeile
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = settingName,
                style = TextStyle(
                    color = textColor,
                    fontSize = settingsTextSize.sp,
                    fontWeight = FontWeight.Normal
                ),
                modifier = Modifier.padding(end = 5.dp)
            )

            OutlinedButton(
                // Klick auf den Button öffnet den ColorPicker
                onClick = { showColorPicker = !showColorPicker },
                // Button-Farbe = aktuell gewählte Farbe
                colors = ButtonDefaults.outlinedButtonColors(
                    containerColor = settingValue
                )
            ) {
                Text(
                    text = "INVISIBLE",  // Oder "Farbe wählen"
                    color = settingValue // Oder dynamisch anpassen
                )
            }
        }

        // AnimatedVisibility für den ColorPicker
        AnimatedVisibility(
            visible = showColorPicker,
            enter = fadeIn() + expandVertically(),
            exit = fadeOut() + shrinkVertically()
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 10.dp)
            ) {
                // Kompakter HsvColorPicker für mehr Platz
                HsvColorPicker(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp)
                        .padding(10.dp),
                    controller = controller,
                    onColorChanged = onColorChanged
                )
                BrightnessSlider(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(10.dp)
                        .height(35.dp),
                    controller = controller,
                )
            }
        }
    }
}

@Composable
fun AppPreferenceSetting(
    viewModel: MainViewModel,
    settingName: String,
    currentApp: AppModel?,
    onClearApp: () -> Unit
) {
    val textColor by viewModel.textColor.collectAsState()
    val primaryColor by viewModel.primaryColor.collectAsState()
    val secondaryColor by viewModel.secondaryColor.collectAsState()

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(5.dp),
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = settingName,
                style = TextStyle(
                    color = textColor,
                    fontSize = settingsTextSize.sp,
                    fontWeight = FontWeight.Normal
                ),
                modifier = Modifier.padding(end = 5.dp)
            )

            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = currentApp?.name ?: stringResource(R.string.settings_app_not_set),
                    style = TextStyle(
                        color = if (currentApp != null) textColor else textColor.copy(alpha = 0.6f),
                        fontSize = (settingsTextSize - 2).sp,
                        fontWeight = FontWeight.Light
                    ),
                    modifier = Modifier.weight(1f, fill = false)
                )

                if (currentApp != null) {
                    OutlinedButton(
                        onClick = onClearApp,
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = textColor
                        ),
                        border = BorderStroke(1.dp, textColor),
                        modifier = Modifier.height(32.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.settings_clear_app_preference),
                            fontSize = 12.sp
                        )
                    }
                }
            }
        }
    }
}
