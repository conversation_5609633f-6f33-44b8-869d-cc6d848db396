package com.kevinnovation.detoxlauncher.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.kevinnovation.detoxlauncher.viewmodel.MainViewModel

/**
 * Simple color theme data class
 */
data class SimpleColorTheme(
    val name: String,
    val primaryColor: Color,
    val secondaryColor: Color
)

/**
 * Predefined simple color themes
 */
object SimpleColorThemes {
    val themes = listOf(
        SimpleColorTheme("Classic Blue", Color(0xFF2196F3), Color(0xFF03DAC6)),
        SimpleColorTheme("Sunset", Color(0xFFFF6B35), Color(0xFFFF8E9B)),
        SimpleColorTheme("Ocean", Color(0xFF0077BE), Color(0xFF00BCD4)),
        SimpleColorTheme("Neon Green", Color(0xFF39FF14), Color(0xFFFF073A)),
        SimpleColorTheme("Purple", Color(0xFF673AB7), Color(0xFFE91E63)),
        SimpleColorTheme("Fire", Color(0xFFD32F2F), Color(0xFFFF5722)),
        SimpleColorTheme("Ice", Color(0xFFE3F2FD), Color(0xFF00E5FF)),
        SimpleColorTheme("Forest", Color(0xFF4CAF50), Color(0xFF8D6E63)),
        SimpleColorTheme("Pastel Pink", Color(0xFFFFB3BA), Color(0xFFBAE1FF)),
        SimpleColorTheme("Gold", Color(0xFFFFD700), Color(0xFFFFA726))
    )
}

/**
 * Simple theme selector component
 */
@Composable
fun SimpleThemeSelector(
    viewModel: MainViewModel,
    modifier: Modifier = Modifier
) {
    val textColor by viewModel.textColor.collectAsState()
    
    Column(
        modifier = modifier.padding(8.dp)
    ) {
        Text(
            text = "Quick Color Themes",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = textColor,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            items(SimpleColorThemes.themes) { theme ->
                SimpleThemeCard(
                    theme = theme,
                    onThemeSelected = {
                        viewModel.setPrimaryColor(theme.primaryColor)
                        viewModel.setSecondaryColor(theme.secondaryColor)
                    }
                )
            }
        }
    }
}

/**
 * Simple theme card component
 */
@Composable
fun SimpleThemeCard(
    theme: SimpleColorTheme,
    onThemeSelected: () -> Unit
) {
    Card(
        modifier = Modifier
            .width(80.dp)
            .height(60.dp)
            .clickable { onThemeSelected() },
        shape = RoundedCornerShape(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    Brush.horizontalGradient(
                        colors = listOf(theme.primaryColor, theme.secondaryColor)
                    )
                )
        ) {
            Text(
                text = theme.name,
                color = Color.White,
                fontSize = 10.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .background(
                        Color.Black.copy(alpha = 0.6f),
                        RoundedCornerShape(4.dp)
                    )
                    .padding(horizontal = 4.dp, vertical = 2.dp)
            )
        }
    }
}

/**
 * Quick color presets for immediate selection
 */
@Composable
fun QuickColorPresets(
    onColorSelected: (Color) -> Unit,
    modifier: Modifier = Modifier
) {
    val quickColors = listOf(
        Color.Red, Color.Green, Color.Blue, Color.Yellow,
        Color.Magenta, Color.Cyan, Color.White, Color.Black,
        Color(0xFFFF9800), Color(0xFF9C27B0), Color(0xFF607D8B), Color(0xFF795548)
    )
    
    Column(modifier = modifier) {
        Text(
            text = "Quick Colors",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            items(quickColors) { color ->
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .clip(RoundedCornerShape(6.dp))
                        .background(color)
                        .border(1.dp, Color.Gray, RoundedCornerShape(6.dp))
                        .clickable { onColorSelected(color) }
                )
            }
        }
    }
}

/**
 * Simple gradient direction selector
 */
@Composable
fun SimpleGradientDirectionSelector(
    onDirectionSelected: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val directions = listOf(
        "→" to "Horizontal",
        "↓" to "Vertical", 
        "↘" to "Diagonal",
        "◉" to "Radial"
    )
    
    Column(modifier = modifier) {
        Text(
            text = "Gradient Direction",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            items(directions) { (symbol, name) ->
                OutlinedButton(
                    onClick = { onDirectionSelected(name) },
                    modifier = Modifier.size(40.dp),
                    contentPadding = PaddingValues(0.dp)
                ) {
                    Text(
                        text = symbol,
                        fontSize = 16.sp
                    )
                }
            }
        }
    }
}

/**
 * Time-based color suggestion
 */
@Composable
fun TimeBasedColorSuggestion(
    viewModel: MainViewModel,
    modifier: Modifier = Modifier
) {
    val textColor by viewModel.textColor.collectAsState()
    
    Column(modifier = modifier) {
        Text(
            text = "Time-based Suggestion",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = textColor,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        OutlinedButton(
            onClick = {
                val calendar = java.util.Calendar.getInstance()
                val hour = calendar.get(java.util.Calendar.HOUR_OF_DAY)
                
                val theme = when (hour) {
                    in 6..11 -> SimpleColorTheme("Morning", Color(0xFFFDD835), Color(0xFFFFB74D))
                    in 12..17 -> SimpleColorTheme("Afternoon", Color(0xFF42A5F5), Color(0xFF66BB6A))
                    in 18..21 -> SimpleColorTheme("Evening", Color(0xFFFF6B35), Color(0xFFD32F2F))
                    else -> SimpleColorTheme("Night", Color(0xFF1A237E), Color(0xFF3F51B5))
                }
                
                viewModel.setPrimaryColor(theme.primaryColor)
                viewModel.setSecondaryColor(theme.secondaryColor)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Apply Time-based Colors", color = textColor)
        }
    }
}

/**
 * Live preview of current colors - same size as original clock
 */
@Composable
fun SimpleColorPreview(
    primaryColor: Color,
    secondaryColor: Color,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "Live Preview",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // Clock preview with same size as original
        Text(
            text = "12:34",
            fontSize = 90.sp, // Same as original DetoxClock
            fontWeight = FontWeight.Bold,
            style = androidx.compose.ui.text.TextStyle(
                brush = Brush.horizontalGradient(
                    colors = listOf(primaryColor, secondaryColor)
                )
            ),
            modifier = Modifier.padding(vertical = 8.dp)
        )

        // Date preview (optional)
        Text(
            text = "Today • Preview",
            fontSize = 20.sp, // Same as original date size
            fontWeight = FontWeight.Normal,
            style = androidx.compose.ui.text.TextStyle(
                brush = Brush.horizontalGradient(
                    colors = listOf(primaryColor, secondaryColor)
                )
            ),
            modifier = Modifier.padding(top = 8.dp)
        )
    }
}

/**
 * Full-size clock preview that matches the original DetoxClock exactly
 */
@Composable
fun FullSizeClockPreview(
    primaryColor: Color,
    secondaryColor: Color,
    tertiaryColor: Color? = null,
    modifier: Modifier = Modifier
) {
    val currentTime = remember {
        val calendar = java.util.Calendar.getInstance()
        val hour = calendar.get(java.util.Calendar.HOUR_OF_DAY)
        val minute = calendar.get(java.util.Calendar.MINUTE)
        String.format("%02d:%02d", hour, minute)
    }

    val currentDate = remember {
        val calendar = java.util.Calendar.getInstance()
        val dayOfMonth = calendar.get(java.util.Calendar.DAY_OF_MONTH)
        val month = calendar.getDisplayName(java.util.Calendar.MONTH, java.util.Calendar.LONG, java.util.Locale.getDefault())
        "$dayOfMonth $month"
    }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Main clock display - exact copy of DetoxClock
        Text(
            text = currentTime,
            style = androidx.compose.ui.text.TextStyle(
                brush = createPreviewBrush(primaryColor, secondaryColor, tertiaryColor),
                fontSize = 90.sp,
                fontWeight = FontWeight.Bold
            ),
            color = Color.Black
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Date display - exact copy of DetoxClock
        Text(
            text = "$currentDate • Preview",
            style = androidx.compose.ui.text.TextStyle(
                brush = Brush.horizontalGradient(
                    colors = listOf(primaryColor, secondaryColor)
                ),
                fontSize = 20.sp,
                fontWeight = FontWeight.Normal
            )
        )
    }
}

/**
 * Creates a brush for preview that supports tertiary colors
 */
private fun createPreviewBrush(
    primaryColor: Color,
    secondaryColor: Color,
    tertiaryColor: Color?
): Brush {
    val colors = if (tertiaryColor != null) {
        listOf(primaryColor, secondaryColor, tertiaryColor)
    } else {
        listOf(primaryColor, secondaryColor)
    }

    return Brush.horizontalGradient(colors)
}

/**
 * Compact clock preview for settings where space is limited
 */
@Composable
fun CompactClockPreview(
    primaryColor: Color,
    secondaryColor: Color,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "Preview:",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )

        Text(
            text = "12:34",
            fontSize = 48.sp, // Smaller than full size but still prominent
            fontWeight = FontWeight.Bold,
            style = androidx.compose.ui.text.TextStyle(
                brush = Brush.horizontalGradient(
                    colors = listOf(primaryColor, secondaryColor)
                )
            )
        )
    }
}
