package com.kevinnovation.detoxlauncher.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.Palette
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.github.skydoves.colorpicker.compose.BrightnessSlider
import com.github.skydoves.colorpicker.compose.ColorEnvelope
import com.github.skydoves.colorpicker.compose.HsvColorPicker
import com.github.skydoves.colorpicker.compose.rememberColorPickerController
import com.kevinnovation.detoxlauncher.R
import com.kevinnovation.detoxlauncher.data.*
import com.kevinnovation.detoxlauncher.viewmodel.MainViewModel
import java.util.*

@Composable
fun EnhancedColorPicker(
    viewModel: MainViewModel,
    settingName: String,
    settingValue: Color,
    modifier: Modifier = Modifier,
    onColorChanged: (ColorEnvelope) -> Unit,
    showThemeSelector: Boolean = true,
    showFavorites: Boolean = true,
    showAccessibilityOptions: Boolean = true
) {
    val textColor by viewModel.textColor.collectAsState()
    val primaryColor by viewModel.primaryColor.collectAsState()
    val secondaryColor by viewModel.secondaryColor.collectAsState()
    
    val controller = rememberColorPickerController()
    var showColorPicker by remember { mutableStateOf(false) }
    var showThemes by remember { mutableStateOf(false) }
    var selectedCategory by remember { mutableStateOf(ThemeCategory.POPULAR) }
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(8.dp)
    ) {
        // Header with title and color preview
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = settingName,
                style = androidx.compose.ui.text.TextStyle(
                    color = textColor,
                    fontSize = 22.sp,
                    fontWeight = FontWeight.Normal
                ),
                modifier = Modifier.weight(1f)
            )
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Theme selector button
                if (showThemeSelector) {
                    IconButton(
                        onClick = { showThemes = !showThemes }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Palette,
                            contentDescription = "Themes",
                            tint = textColor
                        )
                    }
                }
                
                // Color preview button
                OutlinedButton(
                    onClick = { showColorPicker = !showColorPicker },
                    colors = ButtonDefaults.outlinedButtonColors(
                        containerColor = settingValue
                    ),
                    modifier = Modifier.size(48.dp),
                    contentPadding = PaddingValues(0.dp)
                ) {
                    // Empty content - just shows the color
                }
            }
        }
        
        // Theme selector
        AnimatedVisibility(
            visible = showThemes,
            enter = fadeIn() + expandVertically(),
            exit = fadeOut() + shrinkVertically()
        ) {
            ThemeSelector(
                viewModel = viewModel,
                selectedCategory = selectedCategory,
                onCategorySelected = { selectedCategory = it },
                onThemeSelected = { theme ->
                    onColorChanged(ColorEnvelope(theme.primaryColor, "", false))
                    viewModel.setSecondaryColor(theme.secondaryColor)
                    theme.tertiaryColor?.let { viewModel.setTertiaryColor(it) }
                    viewModel.setGradientDirection(theme.gradientDirection)
                    showThemes = false
                }
            )
        }
        
        // Advanced color picker
        AnimatedVisibility(
            visible = showColorPicker,
            enter = fadeIn() + expandVertically(),
            exit = fadeOut() + shrinkVertically()
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 10.dp)
            ) {
                // Quick color presets
                QuickColorPresets(
                    onColorSelected = { color ->
                        onColorChanged(ColorEnvelope(color, "", false))
                    }
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // HSV Color Picker
                HsvColorPicker(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(300.dp)
                        .padding(10.dp),
                    controller = controller,
                    onColorChanged = onColorChanged
                )
                
                // Brightness Slider
                BrightnessSlider(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(10.dp)
                        .height(35.dp),
                    controller = controller,
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Color harmony suggestions
                ColorHarmonySuggestions(
                    baseColor = settingValue,
                    onColorSelected = { color ->
                        onColorChanged(ColorEnvelope(color, "", false))
                    }
                )
                
                if (showAccessibilityOptions) {
                    Spacer(modifier = Modifier.height(16.dp))
                    AccessibilityOptions(viewModel = viewModel)
                }
            }
        }
    }
}

@Composable
fun QuickColorPresets(
    onColorSelected: (Color) -> Unit
) {
    val quickColors = listOf(
        Color.Red, Color.Green, Color.Blue, Color.Yellow,
        Color.Magenta, Color.Cyan, Color.White, Color.Black,
        Color(0xFFFF9800), Color(0xFF9C27B0), Color(0xFF607D8B), Color(0xFF795548)
    )
    
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(horizontal = 8.dp)
    ) {
        items(quickColors) { color ->
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(color)
                    .border(1.dp, Color.Gray, CircleShape)
                    .clickable { onColorSelected(color) }
            )
        }
    }
}

@Composable
fun ColorHarmonySuggestions(
    baseColor: Color,
    onColorSelected: (Color) -> Unit
) {
    val harmonies = remember(baseColor) {
        listOf(
            "Complementary" to ColorUtils.getComplementaryColor(baseColor),
            "Analogous" to ColorUtils.getAnalogousColors(baseColor),
            "Lighter" to ColorUtils.adjustBrightness(baseColor, 1.3f),
            "Darker" to ColorUtils.adjustBrightness(baseColor, 0.7f)
        )
    }
    
    Column {
        Text(
            text = "Color Harmony",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 8.dp)
        ) {
            items(harmonies) { (name, colors) ->
                when (colors) {
                    is Color -> {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(40.dp)
                                    .clip(CircleShape)
                                    .background(colors)
                                    .border(1.dp, Color.Gray, CircleShape)
                                    .clickable { onColorSelected(colors) }
                            )
                            Text(
                                text = name,
                                fontSize = 10.sp,
                                modifier = Modifier.padding(top = 4.dp)
                            )
                        }
                    }
                    is List<*> -> {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Row(horizontalArrangement = Arrangement.spacedBy(2.dp)) {
                                (colors as List<Color>).take(3).forEach { color ->
                                    Box(
                                        modifier = Modifier
                                            .size(12.dp)
                                            .clip(CircleShape)
                                            .background(color)
                                            .clickable { onColorSelected(color) }
                                    )
                                }
                            }
                            Text(
                                text = name,
                                fontSize = 10.sp,
                                modifier = Modifier.padding(top = 4.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun AccessibilityOptions(
    viewModel: MainViewModel
) {
    val highContrast by viewModel.accessibilityHighContrast.collectAsState()
    val colorBlindFriendly by viewModel.accessibilityColorBlindFriendly.collectAsState()
    val autoBrightness by viewModel.accessibilityAutoBrightness.collectAsState()
    
    Column {
        Text(
            text = "Accessibility",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(8.dp)
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text("High Contrast", fontSize = 14.sp)
            Switch(
                checked = highContrast,
                onCheckedChange = { viewModel.setAccessibilityHighContrast(it) }
            )
        }
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text("Color Blind Friendly", fontSize = 14.sp)
            Switch(
                checked = colorBlindFriendly,
                onCheckedChange = { viewModel.setAccessibilityColorBlindFriendly(it) }
            )
        }
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text("Auto Brightness", fontSize = 14.sp)
            Switch(
                checked = autoBrightness,
                onCheckedChange = { viewModel.setAccessibilityAutoBrightness(it) }
            )
        }
    }
}
